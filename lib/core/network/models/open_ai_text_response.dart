class AiTextResponse {
  final String? id;
  final String? object;
  final int? created;
  final String? model;
  final List<AiTextChoice>? choices;
  final AiTextUsage? usage;
  final AiTextError? error;

  const AiTextResponse({
    this.id,
    this.object,
    this.created,
    this.model,
    this.choices,
    this.usage,
    this.error,
  });

  factory AiTextResponse.from(Map<String, dynamic> json) {
    return AiTextResponse(
      id: json['id'] as String?,
      object: json['object'] as String?,
      created: json['created'] as int?,
      model: json['model'] as String?,
      choices: json['choices'] != null
          ? (json['choices'] as List)
              .map((choice) => AiTextChoice.from(choice as Map<String, dynamic>))
              .toList()
          : null,
      usage: json['usage'] != null
          ? AiTextUsage.from(json['usage'] as Map<String, dynamic>)
          : null,
      error: json['error'] != null
          ? AiTextError.from(json['error'] as Map<String, dynamic>)
          : null,
    );
  }

  factory AiTextResponse.failure(String? error, [int? statusCode]) {
    return AiTextResponse(
      error: AiTextError(
        code: statusCode?.toString(),
        message: error,
      ),
    );
  }

  bool get isSuccess => error == null && choices != null && choices!.isNotEmpty;

  String? get content {
    if (choices != null && choices!.isNotEmpty) {
      return choices!.first.message?.content;
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'object': object,
      'created': created,
      'model': model,
      'choices': choices?.map((choice) => choice.toJson()).toList(),
      'usage': usage?.toJson(),
      'error': error?.toJson(),
    };
  }
}

class AiTextChoice {
  final int? index;
  final AiTextMessage? message;
  final String? finishReason;

  const AiTextChoice({
    this.index,
    this.message,
    this.finishReason,
  });

  factory AiTextChoice.from(Map<String, dynamic> json) {
    return AiTextChoice(
      index: json['index'] as int?,
      message: json['message'] != null
          ? AiTextMessage.from(json['message'] as Map<String, dynamic>)
          : null,
      finishReason: json['finish_reason'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'message': message?.toJson(),
      'finish_reason': finishReason,
    };
  }
}

class AiTextMessage {
  final String? role;
  final String? content;

  const AiTextMessage({
    this.role,
    this.content,
  });

  factory AiTextMessage.from(Map<String, dynamic> json) {
    return AiTextMessage(
      role: json['role'] as String?,
      content: json['content'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'content': content,
    };
  }
}

class AiTextUsage {
  final int? promptTokens;
  final int? completionTokens;
  final int? totalTokens;

  const AiTextUsage({
    this.promptTokens,
    this.completionTokens,
    this.totalTokens,
  });

  factory AiTextUsage.from(Map<String, dynamic> json) {
    return AiTextUsage(
      promptTokens: json['prompt_tokens'] as int?,
      completionTokens: json['completion_tokens'] as int?,
      totalTokens: json['total_tokens'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'prompt_tokens': promptTokens,
      'completion_tokens': completionTokens,
      'total_tokens': totalTokens,
    };
  }
}

class AiTextError {
  final String? code;
  final String? message;
  final String? type;

  const AiTextError({
    this.code,
    this.message,
    this.type,
  });

  factory AiTextError.from(Map<String, dynamic> json) {
    return AiTextError(
      code: json['code'] as String?,
      message: json['message'] as String?,
      type: json['type'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'type': type,
    };
  }
}
