import 'dart:convert';

class AiTextRequest {
  final String model;
  final List<Map<String, String>> messages;
  final double temperature;
  final int maxTokens;
  final double topP;
  final double frequencyPenalty;
  final double presencePenalty;

  const AiTextRequest({
    this.model = 'gpt-3.5-turbo',
    required this.messages,
    this.temperature = 0.7,
    this.maxTokens = 1000,
    this.topP = 1.0,
    this.frequencyPenalty = 0.0,
    this.presencePenalty = 0.0,
  });

  Map<String, dynamic> get data {
    return {
      'model': model,
      'messages': messages,
      'temperature': temperature,
      'max_tokens': maxTokens,
      'top_p': topP,
      'frequency_penalty': frequencyPenalty,
      'presence_penalty': presencePenalty,
    };
  }

  String get body => jsonEncode(data);

  // Helper method to create a cover letter request
  factory AiTextRequest.coverLetter({
    required String resumeData,
    required String jobDescription,
    required String companyName,
    required String positionTitle,
  }) {
    final prompt = '''
Create a professional cover letter based on the following information:

RESUME DATA:
$resumeData

JOB DESCRIPTION:
$jobDescription

COMPANY: $companyName
POSITION: $positionTitle

Please write a compelling cover letter that:
1. Highlights relevant experience from the resume
2. Shows enthusiasm for the specific role and company
3. Demonstrates how the candidate's skills match the job requirements
4. Maintains a professional yet engaging tone
5. Is concise and well-structured (3-4 paragraphs)

Format the cover letter with proper business letter structure including:
- Date
- Company address placeholder
- Salutation
- Body paragraphs
- Professional closing
- Signature line

Make it personalized and specific to this role, not generic.
''';

    return AiTextRequest(
      model: 'gpt-3.5-turbo',
      messages: [
        {
          'role': 'system',
          'content': 'You are a professional career counselor and expert cover letter writer. Create compelling, personalized cover letters that help candidates stand out.',
        },
        {
          'role': 'user',
          'content': prompt,
        },
      ],
      temperature: 0.7,
      maxTokens: 1500,
    );
  }
}
