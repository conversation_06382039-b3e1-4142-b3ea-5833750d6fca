import 'dart:io';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdService {
  static AdService? _instance;
  static AdService get instance => _instance ??= AdService._();
  
  AdService._();

  // Test Ad Unit IDs (replace with your real ones in production)
  static final String _bannerAdUnitId = Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/6300978111' // Test banner ad unit ID for Android
      : 'ca-app-pub-3940256099942544/2934735716'; // Test banner ad unit ID for iOS

  static final String _interstitialAdUnitId = Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/1033173712' // Test interstitial ad unit ID for Android
      : 'ca-app-pub-3940256099942544/4411468910'; // Test interstitial ad unit ID for iOS

  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  bool _isInterstitialAdReady = false;

  /// Initialize Mobile Ads SDK
  static Future<void> initialize() async {
    try {
      await MobileAds.instance.initialize();
      print('Google Mobile Ads initialized successfully');
    } catch (e) {
      print('Failed to initialize Google Mobile Ads: $e');
      // Don't rethrow the error to prevent app crash
      // The app can still function without ads
    }
  }

  /// Create and load banner ad
  BannerAd createBannerAd({
    required Function(Ad ad) onAdLoaded,
    required Function(Ad ad, LoadAdError error) onAdFailedToLoad,
  }) {
    return BannerAd(
      adUnitId: _bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: onAdLoaded,
        onAdFailedToLoad: onAdFailedToLoad,
        onAdOpened: (Ad ad) => print('Banner ad opened'),
        onAdClosed: (Ad ad) => print('Banner ad closed'),
      ),
    );
  }

  /// Load banner ad for My Resumes page
  void loadBannerAd({
    required Function(BannerAd ad) onAdLoaded,
    required Function(LoadAdError error) onAdFailedToLoad,
  }) {
    _bannerAd?.dispose();
    
    _bannerAd = createBannerAd(
      onAdLoaded: (ad) {
        print('Banner ad loaded successfully');
        onAdLoaded(ad as BannerAd);
      },
      onAdFailedToLoad: (ad, error) {
        print('Banner ad failed to load: $error');
        ad.dispose();
        onAdFailedToLoad(error);
      },
    );
    
    _bannerAd!.load();
  }

  /// Load interstitial ad for Resume Builder page
  void loadInterstitialAd({
    Function()? onAdLoaded,
    Function(LoadAdError error)? onAdFailedToLoad,
  }) {
    InterstitialAd.load(
      adUnitId: _interstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (InterstitialAd ad) {
          print('Interstitial ad loaded successfully');
          _interstitialAd = ad;
          _isInterstitialAdReady = true;
          
          _interstitialAd!.setImmersiveMode(true);
          onAdLoaded?.call();
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('Interstitial ad failed to load: $error');
          _isInterstitialAdReady = false;
          onAdFailedToLoad?.call(error);
        },
      ),
    );
  }

  /// Show interstitial ad
  void showInterstitialAd({
    Function()? onAdDismissed,
    Function()? onAdFailedToShow,
  }) {
    if (_isInterstitialAdReady && _interstitialAd != null) {
      _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdShowedFullScreenContent: (InterstitialAd ad) {
          print('Interstitial ad showed full screen content');
        },
        onAdDismissedFullScreenContent: (InterstitialAd ad) {
          print('Interstitial ad dismissed');
          ad.dispose();
          _isInterstitialAdReady = false;
          _interstitialAd = null;
          onAdDismissed?.call();
          
          // Load next interstitial ad
          loadInterstitialAd();
        },
        onAdFailedToShowFullScreenContent: (InterstitialAd ad, AdError error) {
          print('Interstitial ad failed to show: $error');
          ad.dispose();
          _isInterstitialAdReady = false;
          _interstitialAd = null;
          onAdFailedToShow?.call();
          
          // Load next interstitial ad
          loadInterstitialAd();
        },
      );
      
      _interstitialAd!.show();
    } else {
      print('Interstitial ad is not ready yet');
      onAdFailedToShow?.call();
    }
  }

  /// Check if interstitial ad is ready
  bool get isInterstitialAdReady => _isInterstitialAdReady;

  /// Dispose banner ad
  void disposeBannerAd() {
    _bannerAd?.dispose();
    _bannerAd = null;
  }

  /// Dispose interstitial ad
  void disposeInterstitialAd() {
    _interstitialAd?.dispose();
    _interstitialAd = null;
    _isInterstitialAdReady = false;
  }

  /// Dispose all ads
  void dispose() {
    disposeBannerAd();
    disposeInterstitialAd();
  }
}
