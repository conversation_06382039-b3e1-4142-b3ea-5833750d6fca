import 'dart:async';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

import '../../data/models/purchase_model.dart';
import 'purchase_availability_service.dart';

class InAppPurchaseService {
  static final InAppPurchaseService _instance = InAppPurchaseService._internal();
  factory InAppPurchaseService() => _instance;
  InAppPurchaseService._internal();

  // Purchase availability service - will be injected
  PurchaseAvailabilityService? _purchaseAvailabilityService;

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  // Product IDs - these should match your Google Play Console products
  static const String premiumMonthlyId = 'premium_monthly';
  static const String premiumYearlyId = 'premium_yearly';
  static const String premiumLifetimeId = 'premium_lifetime';
  
  static const List<String> productIds = [
    premiumMonthlyId,
    premiumYearlyId,
    premiumLifetimeId,
  ];

  // Stream controllers for purchase events
  final StreamController<List<PurchaseProductModel>> _productsController = 
      StreamController<List<PurchaseProductModel>>.broadcast();
  final StreamController<PurchaseModel> _purchaseController = 
      StreamController<PurchaseModel>.broadcast();
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();

  // Getters for streams
  Stream<List<PurchaseProductModel>> get productsStream => _productsController.stream;
  Stream<PurchaseModel> get purchaseStream => _purchaseController.stream;
  Stream<String> get errorStream => _errorController.stream;

  bool _isInitialized = false;
  List<PurchaseProductModel> _availableProducts = [];

  /// Set the purchase availability service
  void setPurchaseAvailabilityService(PurchaseAvailabilityService service) {
    _purchaseAvailabilityService = service;
  }

  /// Initialize the in-app purchase service
  Future<bool> initialize() async {
    try {
      debugPrint('DEBUG: IAP - Initializing In-App Purchase service');

      // Check if purchases are enabled via remote config
      if (_purchaseAvailabilityService != null && !_purchaseAvailabilityService!.arePurchasesEnabled()) {
        debugPrint('DEBUG: IAP - In-App Purchases disabled via remote config');
        _errorController.add(_purchaseAvailabilityService!.getDisabledMessage());
        return false;
      }

      final bool isAvailable = await _inAppPurchase.isAvailable();
      if (!isAvailable) {
        debugPrint('DEBUG: IAP - In-App Purchase not available');
        _errorController.add('In-App Purchase not available on this device');
        return false;
      }

      // Note: Pending purchases are automatically enabled in newer versions

      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _handlePurchaseUpdates,
        onError: (error) {
          debugPrint('DEBUG: IAP - Purchase stream error: $error');
          _errorController.add('Purchase error: $error');
        },
      );

      // Load available products
      await _loadProducts();

      _isInitialized = true;
      debugPrint('DEBUG: IAP - Service initialized successfully');
      return true;
    } catch (e) {
      debugPrint('DEBUG: IAP - Initialization error: $e');
      _errorController.add('Failed to initialize purchases: $e');
      return false;
    }
  }

  /// Load available products from the store
  Future<void> _loadProducts() async {
    try {
      debugPrint('DEBUG: IAP - Loading products: $productIds');
      debugPrint('DEBUG: IAP - Requesting products: ${productIds.join(', ')}');

      // Check if we're in debug mode and should use fake products
      bool isDebugMode = false;
      assert(() {
        isDebugMode = true;
        return true;
      }());

      if (isDebugMode) {
        debugPrint('DEBUG: IAP - 🚨 RUNNING IN DEBUG MODE');
        debugPrint('DEBUG: IAP - 🚨 In-app purchases only work with RELEASE builds installed from Google Play Console');
        debugPrint('DEBUG: IAP - 💡 To test properly: flutter build appbundle --release, upload to Internal Testing');

        // Create fake products for UI testing in debug mode
        _availableProducts = _createFakeProducts();
        debugPrint('DEBUG: IAP - 🎭 Using fake products for debug mode testing');
        for (final product in _availableProducts) {
          debugPrint('DEBUG: IAP - 🎭 Fake: ${product.id} - ${product.title} (${product.price})');
        }
        _productsController.add(_availableProducts);
        return;
      }

      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(
        productIds.toSet(),
      );

      if (response.error != null) {
        debugPrint('DEBUG: IAP - Product query error: ${response.error}');
        debugPrint('DEBUG: IAP - Error code: ${response.error!.code}');
        debugPrint('DEBUG: IAP - Error message: ${response.error!.message}');
        debugPrint('DEBUG: IAP - Error details: ${response.error!.details}');
        _errorController.add('Failed to load products: ${response.error!.message}');
        return;
      }

      debugPrint('DEBUG: IAP - Found products: ${response.productDetails.map((p) => p.id).join(', ')}');
      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('DEBUG: IAP - Products not found: ${response.notFoundIDs.join(', ')}');
        debugPrint('DEBUG: IAP - ⚠️  MISSING PRODUCTS: These product IDs are not configured in Google Play Console:');
        for (final missingId in response.notFoundIDs) {
          debugPrint('DEBUG: IAP - ❌ Missing: $missingId');
        }
        debugPrint('DEBUG: IAP - 🚨 SOLUTION: You\'re in debug mode! Products only work in release builds from Play Console.');
      }

      _availableProducts = response.productDetails
          .map((product) => PurchaseProductModel.fromProductDetails(product))
          .toList();

      debugPrint('DEBUG: IAP - Successfully loaded ${_availableProducts.length} products');
      for (final product in _availableProducts) {
        debugPrint('DEBUG: IAP - ✅ Available: ${product.id} - ${product.title} (${product.price})');
      }

      _productsController.add(_availableProducts);
    } catch (e) {
      debugPrint('DEBUG: IAP - Load products error: $e');
      _errorController.add('Failed to load products: $e');
    }
  }

  /// Create fake products for debug mode testing
  List<PurchaseProductModel> _createFakeProducts() {
    return [
      PurchaseProductModel(
        id: 'premium_monthly',
        title: 'Premium Monthly (DEBUG)',
        description: 'Get unlimited PDF exports and premium features for one month',
        price: '\$4.99',
        rawPrice: 4.99,
        currencyCode: 'USD',
        isAvailable: true,
      ),
      PurchaseProductModel(
        id: 'premium_yearly',
        title: 'Premium Yearly (DEBUG)',
        description: 'Get unlimited PDF exports and premium features for one year. Best value!',
        price: '\$29.99',
        rawPrice: 29.99,
        currencyCode: 'USD',
        isAvailable: true,
      ),
      PurchaseProductModel(
        id: 'premium_lifetime',
        title: 'Premium Lifetime (DEBUG)',
        description: 'Get unlimited PDF exports and premium features forever with a one-time payment',
        price: '\$99.99',
        rawPrice: 99.99,
        currencyCode: 'USD',
        isAvailable: true,
      ),
    ];
  }

  /// Handle purchase updates from the store
  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      debugPrint('DEBUG: IAP - Purchase update: ${purchaseDetails.status} for ${purchaseDetails.productID}');
      
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          debugPrint('DEBUG: IAP - Purchase pending: ${purchaseDetails.productID}');
          break;
        case PurchaseStatus.purchased:
          debugPrint('DEBUG: IAP - Purchase completed: ${purchaseDetails.productID}');
          _handleSuccessfulPurchase(purchaseDetails);
          break;
        case PurchaseStatus.error:
          debugPrint('DEBUG: IAP - Purchase error: ${purchaseDetails.error}');
          _errorController.add('Purchase failed: ${purchaseDetails.error?.message ?? "Unknown error"}');
          break;
        case PurchaseStatus.restored:
          debugPrint('DEBUG: IAP - Purchase restored: ${purchaseDetails.productID}');
          _handleSuccessfulPurchase(purchaseDetails);
          break;
        case PurchaseStatus.canceled:
          debugPrint('DEBUG: IAP - Purchase canceled: ${purchaseDetails.productID}');
          break;
      }

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// Handle successful purchase
  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    // Create purchase model (userId will be set by the calling code)
    final purchase = PurchaseModel.fromPurchaseDetails(purchaseDetails, '');
    _purchaseController.add(purchase);
  }

  /// Get available products
  List<PurchaseProductModel> get availableProducts => _availableProducts;

  /// Purchase a product
  Future<bool> purchaseProduct(String productId) async {
    try {
      // Check if purchases are enabled
      if (_purchaseAvailabilityService != null && !_purchaseAvailabilityService!.arePurchasesEnabled()) {
        debugPrint('DEBUG: IAP - Purchase blocked - disabled via remote config');
        _errorController.add(_purchaseAvailabilityService!.getDisabledMessage());
        return false;
      }

      if (!_isInitialized) {
        debugPrint('DEBUG: IAP - Service not initialized');
        _errorController.add('Purchase service not initialized');
        return false;
      }

      debugPrint('DEBUG: IAP - Attempting to purchase: $productId');

      // Find the product
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails({productId});
      
      if (response.productDetails.isEmpty) {
        debugPrint('DEBUG: IAP - Product not found: $productId');
        _errorController.add('Product not found: $productId');
        return false;
      }

      final ProductDetails productDetails = response.productDetails.first;
      
      // Create purchase param
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      // Start the purchase
      bool success;
      if (productIds.contains(productId)) {
        // For subscriptions or non-consumables
        success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        success = await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
      }

      debugPrint('DEBUG: IAP - Purchase initiated: $success');
      return success;
    } catch (e) {
      debugPrint('DEBUG: IAP - Purchase error: $e');
      _errorController.add('Purchase failed: $e');
      return false;
    }
  }

  /// Restore purchases
  Future<void> restorePurchases() async {
    try {
      // Check if purchases are enabled
      if (_purchaseAvailabilityService != null && !_purchaseAvailabilityService!.arePurchasesEnabled()) {
        debugPrint('DEBUG: IAP - Restore blocked - disabled via remote config');
        _errorController.add(_purchaseAvailabilityService!.getDisabledMessage());
        return;
      }

      debugPrint('DEBUG: IAP - Restoring purchases');
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint('DEBUG: IAP - Restore error: $e');
      _errorController.add('Failed to restore purchases: $e');
    }
  }

  /// Get product by ID
  PurchaseProductModel? getProductById(String productId) {
    try {
      return _availableProducts.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Dispose the service
  void dispose() {
    _subscription.cancel();
    _productsController.close();
    _purchaseController.close();
    _errorController.close();
  }
}
