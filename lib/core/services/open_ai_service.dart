import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:resume_builder/core/network/models/open_ai_text_request.dart';
import 'package:resume_builder/core/network/models/open_ai_text_response.dart';



class AiGenerator {
  /// OPENAI API KEY
  final String key;

  const AiGenerator({
    /// OPENAI API KEY
    required this.key,
  });

  static AiGenerator? _i;

  static AiGenerator get i {
    if (_i != null) {
      return _i!;
    } else {
      throw UnimplementedError("AiGenerator not initialized yet!");
    }
  }

  static void init({
    /// OPENAI API KEY
    required String key,
  }) {
    _i = AiGenerator(key: key);
  }


  /// Generate text using OpenAI's chat completion API
  Future<AiTextResponse> generateText(AiTextRequest request) async {
    try {
      final response = await http.post(
        Uri.parse("https://api.openai.com/v1/chat/completions"),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $key",
        },
        body: request.body,
      );

      if (response.statusCode == 200) {
        final raw = jsonDecode(response.body);
        if (raw is Map<String, dynamic>) {
          return AiTextResponse.from(raw);
        }
      }

      return AiTextResponse.failure(
        response.reasonPhrase ?? response.body,
        response.statusCode,
      );
    } catch (e) {
      return AiTextResponse.failure(e.toString());
    }
  }

  /// Generate a cover letter based on resume data and job description
  Future<AiTextResponse> generateCoverLetter({
    required String resumeData,
    required String jobDescription,
    required String companyName,
    required String positionTitle,
  }) async {
    final request = AiTextRequest.coverLetter(
      resumeData: resumeData,
      jobDescription: jobDescription,
      companyName: companyName,
      positionTitle: positionTitle,
    );

    return generateText(request);
  }
}