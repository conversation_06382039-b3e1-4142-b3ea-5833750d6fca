import '../../domain/repositories/remote_config_repository.dart';

/// Service to determine if in-app purchases should be available
class PurchaseAvailabilityService {
  final RemoteConfigRepository _remoteConfigRepository;

  PurchaseAvailabilityService(this._remoteConfigRepository);

  /// Check if in-app purchases are enabled via remote config
  bool arePurchasesEnabled() {
    try {
      return _remoteConfigRepository.areInAppPurchasesEnabled();
    } catch (e) {
      // Default to enabled if there's an error reading config
      return true;
    }
  }

  /// Get a user-friendly message when purchases are disabled
  String getDisabledMessage() {
    return 'In-app purchases are temporarily unavailable. Please try again later.';
  }

  /// Get a detailed message for developers/support
  String getDisabledDetailedMessage() {
    return 'In-app purchases have been disabled via remote configuration. '
           'This may be due to maintenance, policy updates, or regional restrictions.';
  }

  /// Check if purchases are enabled and throw an exception if not
  void ensurePurchasesEnabled() {
    if (!arePurchasesEnabled()) {
      throw PurchaseUnavailableException(getDisabledMessage());
    }
  }
}

/// Exception thrown when purchases are not available
class PurchaseUnavailableException implements Exception {
  final String message;
  
  const PurchaseUnavailableException(this.message);
  
  @override
  String toString() => 'PurchaseUnavailableException: $message';
}
