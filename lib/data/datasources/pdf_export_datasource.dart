import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';

import '../models/simple_resume_model.dart';
import '../models/resume_template_model.dart';

class PdfExportDataSource {
  Future<void> exportToPdf(ResumeModel resume, {ResumeTemplateModel? template}) async {
    try {
      debugPrint('DEBUG: PDF Export - Starting export with template: ${template?.name ?? 'null'} (ID: ${template?.id ?? 'null'})');

      final pdf = pw.Document();

      // Get template spacing
      final sectionSpacing = template?.style.sectionSpacing ?? 20.0;

      // Add pages to PDF
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              _buildHeader(resume.personalInfo, template),
              pw.SizedBox(height: sectionSpacing),
              if (resume.summary.isNotEmpty) ...[
                _buildSection('Summary', resume.summary, template),
                pw.SizedBox(height: sectionSpacing),
              ],
              if (resume.workExperience.isNotEmpty) ...[
                _buildWorkExperienceSection(resume.workExperience, template),
                pw.SizedBox(height: sectionSpacing),
              ],
              if (resume.education.isNotEmpty) ...[
                _buildEducationSection(resume.education, template),
                pw.SizedBox(height: sectionSpacing),
              ],
              if (resume.skills.isNotEmpty) ...[
                _buildSkillsSection(resume.skills, template),
                pw.SizedBox(height: sectionSpacing),
              ],
              if (resume.projects.isNotEmpty) ...[
                _buildProjectsSection(resume.projects, template),
                pw.SizedBox(height: sectionSpacing),
              ],
              if (resume.certifications.isNotEmpty) ...[
                _buildCertificationsSection(resume.certifications, template),
                pw.SizedBox(height: sectionSpacing),
              ],
              if (resume.languages.isNotEmpty) ...[
                _buildLanguagesSection(resume.languages, template),
              ],
            ];
          },
        ),
      );

      debugPrint('DEBUG: PDF Export - PDF generation completed successfully');

      // Save or print the PDF
      await _savePdf(pdf, resume.personalInfo.fullName);
    } catch (e) {
      debugPrint('DEBUG: PDF Export - Error: $e');
      throw Exception('Failed to export PDF: $e');
    }
  }

  // Utility method to convert Flutter Color to PDF Color
  PdfColor _convertColor(Color flutterColor) {
    return PdfColor.fromRYB(
      (flutterColor.r * 255.0).round() / 255.0,
      (flutterColor.g * 255.0).round() / 255.0,
      (flutterColor.b * 255.0).round() / 255.0,
    );
  }

  pw.Widget _buildHeader(PersonalInfoModel personalInfo, ResumeTemplateModel? template) {
    // Use template styling if available, otherwise use defaults
    final headerFontSize = template?.style.headerFontSize ?? 24.0;
    final bodyFontSize = template?.style.bodyFontSize ?? 14.0;
    final primaryColor = template?.style.primaryColor;
    final secondaryColor = template?.style.secondaryColor;

    debugPrint('DEBUG: PDF Header - Using template colors: primary=${primaryColor?.toString()}, secondary=${secondaryColor?.toString()}');

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Name with primary color
        pw.Text(
          personalInfo.fullName,
          style: pw.TextStyle(
            fontSize: headerFontSize,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
          ),
        ),
        pw.SizedBox(height: 8),
        // Contact info with secondary color
        pw.Row(
          children: [
            pw.Text(
              '${personalInfo.email} | ${personalInfo.phone}',
              style: pw.TextStyle(
                fontSize: bodyFontSize,
                color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey700,
              ),
            ),
          ],
        ),
        pw.Text(
          '${personalInfo.address}, ${personalInfo.city}, ${personalInfo.state} ${personalInfo.zipCode}',
          style: pw.TextStyle(
            fontSize: bodyFontSize,
            color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey700,
          ),
        ),
        // Add a colored line separator if template has accent color
        if (template?.style.accentColor != null) ...[
          pw.SizedBox(height: 8),
          pw.Container(
            height: 2,
            width: double.infinity,
            color: _convertColor(template!.style.accentColor),
          ),
        ],
      ],
    );
  }

  pw.Widget _buildSection(String title, String content, ResumeTemplateModel? template) {
    final bodyFontSize = template?.style.bodyFontSize ?? 14.0;
    final primaryColor = template?.style.primaryColor;
    final accentColor = template?.style.accentColor;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title with primary color
        pw.Text(
          title,
          style: pw.TextStyle(
            fontSize: bodyFontSize + 2,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
          ),
        ),
        // Add accent line under section title if accent color is available
        if (accentColor != null) ...[
          pw.SizedBox(height: 4),
          pw.Container(
            height: 1,
            width: 50,
            color: _convertColor(accentColor),
          ),
        ],
        pw.SizedBox(height: 8),
        pw.Text(
          content,
          style: pw.TextStyle(
            fontSize: bodyFontSize,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  pw.Widget _buildWorkExperienceSection(List<WorkExperienceModel> experiences, ResumeTemplateModel? template) {
    final bodyFontSize = template?.style.bodyFontSize ?? 14.0;
    final primaryColor = template?.style.primaryColor;
    final secondaryColor = template?.style.secondaryColor;
    final accentColor = template?.style.accentColor;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title with primary color
        pw.Text(
          'Work Experience',
          style: pw.TextStyle(
            fontSize: bodyFontSize + 2,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
          ),
        ),
        // Add accent line under section title if accent color is available
        if (accentColor != null) ...[
          pw.SizedBox(height: 4),
          pw.Container(
            height: 1,
            width: 50,
            color: _convertColor(accentColor),
          ),
        ],
        pw.SizedBox(height: 8),
        ...experiences.map((exp) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 16),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    exp.jobTitle,
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: bodyFontSize + 1,
                      color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
                    ),
                  ),
                  pw.Text(
                    _formatDateRange(exp.startDate, exp.endDate, exp.isCurrentJob),
                    style: pw.TextStyle(
                      fontSize: bodyFontSize - 1,
                      color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey700,
                    ),
                  ),
                ],
              ),
              pw.Text(
                '${exp.company} - ${exp.location}',
                style: pw.TextStyle(
                  fontSize: bodyFontSize,
                  color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                exp.description,
                style: pw.TextStyle(
                  fontSize: bodyFontSize,
                  color: PdfColors.black,
                ),
              ),
              if (exp.achievements.isNotEmpty) ...[
                pw.SizedBox(height: 4),
                ...exp.achievements.map((achievement) => pw.Bullet(
                  text: achievement,
                  style: pw.TextStyle(
                    fontSize: bodyFontSize,
                    color: PdfColors.black,
                  ),
                )),
              ],
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildEducationSection(List<EducationModel> education, ResumeTemplateModel? template) {
    final bodyFontSize = template?.style.bodyFontSize ?? 14.0;
    final primaryColor = template?.style.primaryColor;
    final secondaryColor = template?.style.secondaryColor;
    final accentColor = template?.style.accentColor;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title with primary color
        pw.Text(
          'Education',
          style: pw.TextStyle(
            fontSize: bodyFontSize + 2,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
          ),
        ),
        // Add accent line under section title if accent color is available
        if (accentColor != null) ...[
          pw.SizedBox(height: 4),
          pw.Container(
            height: 1,
            width: 50,
            color: _convertColor(accentColor),
          ),
        ],
        pw.SizedBox(height: 8),
        ...education.map((edu) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 12),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    edu.degree,
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: bodyFontSize,
                      color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
                    ),
                  ),
                  pw.Text(
                    _formatDateRange(edu.startDate, edu.endDate, edu.isCurrentlyStudying),
                    style: pw.TextStyle(
                      fontSize: bodyFontSize - 1,
                      color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey700,
                    ),
                  ),
                ],
              ),
              pw.Text(
                '${edu.institution} - ${edu.location}',
                style: pw.TextStyle(
                  fontSize: bodyFontSize,
                  color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey800,
                ),
              ),
              if (edu.gpa != null) pw.Text(
                'GPA: ${edu.gpa}',
                style: pw.TextStyle(
                  fontSize: bodyFontSize,
                  color: PdfColors.black,
                ),
              ),
              if (edu.description != null) pw.Text(
                edu.description!,
                style: pw.TextStyle(
                  fontSize: bodyFontSize,
                  color: PdfColors.black,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildSkillsSection(List<SkillCategoryModel> skillCategories, ResumeTemplateModel? template) {
    final bodyFontSize = template?.style.bodyFontSize ?? 14.0;
    final primaryColor = template?.style.primaryColor;
    final accentColor = template?.style.accentColor;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title with primary color
        pw.Text(
          'Skills',
          style: pw.TextStyle(
            fontSize: bodyFontSize + 2,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
          ),
        ),
        // Add accent line under section title if accent color is available
        if (accentColor != null) ...[
          pw.SizedBox(height: 4),
          pw.Container(
            height: 1,
            width: 50,
            color: _convertColor(accentColor),
          ),
        ],
        pw.SizedBox(height: 8),
        ...skillCategories.map((category) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 8),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                category.category,
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  fontSize: bodyFontSize,
                  color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
                ),
              ),
              pw.Text(
                category.skills.map((skill) => skill.name).join(', '),
                style: pw.TextStyle(
                  fontSize: bodyFontSize,
                  color: PdfColors.black,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildProjectsSection(List<ProjectModel> projects, ResumeTemplateModel? template) {
    final bodyFontSize = template?.style.bodyFontSize ?? 14.0;
    final primaryColor = template?.style.primaryColor;
    final secondaryColor = template?.style.secondaryColor;
    final accentColor = template?.style.accentColor;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title with primary color
        pw.Text(
          'Projects',
          style: pw.TextStyle(
            fontSize: bodyFontSize + 2,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
          ),
        ),
        // Add accent line under section title if accent color is available
        if (accentColor != null) ...[
          pw.SizedBox(height: 4),
          pw.Container(
            height: 1,
            width: 50,
            color: _convertColor(accentColor),
          ),
        ],
        pw.SizedBox(height: 8),
        ...projects.map((project) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 12),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                project.name,
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  fontSize: bodyFontSize,
                  color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
                ),
              ),
              pw.Text(
                project.description,
                style: pw.TextStyle(
                  fontSize: bodyFontSize,
                  color: PdfColors.black,
                ),
              ),
              if (project.technologies.isNotEmpty)
                pw.Text(
                  'Technologies: ${project.technologies.join(', ')}',
                  style: pw.TextStyle(
                    fontSize: bodyFontSize - 1,
                    color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey700,
                  ),
                ),
              if (project.projectUrl != null) pw.Text(
                'URL: ${project.projectUrl}',
                style: pw.TextStyle(
                  fontSize: bodyFontSize - 1,
                  color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey700,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildCertificationsSection(List<CertificationModel> certifications, ResumeTemplateModel? template) {
    final bodyFontSize = template?.style.bodyFontSize ?? 14.0;
    final primaryColor = template?.style.primaryColor;
    final secondaryColor = template?.style.secondaryColor;
    final accentColor = template?.style.accentColor;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title with primary color
        pw.Text(
          'Certifications',
          style: pw.TextStyle(
            fontSize: bodyFontSize + 2,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
          ),
        ),
        // Add accent line under section title if accent color is available
        if (accentColor != null) ...[
          pw.SizedBox(height: 4),
          pw.Container(
            height: 1,
            width: 50,
            color: _convertColor(accentColor),
          ),
        ],
        pw.SizedBox(height: 8),
        ...certifications.map((cert) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 8),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      cert.name,
                      style: pw.TextStyle(
                        fontWeight: pw.FontWeight.bold,
                        fontSize: bodyFontSize,
                        color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
                      ),
                    ),
                    pw.Text(
                      cert.issuer,
                      style: pw.TextStyle(
                        fontSize: bodyFontSize,
                        color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey800,
                      ),
                    ),
                  ],
                ),
              ),
              pw.Text(
                DateFormat('MMM yyyy').format(cert.issueDate),
                style: pw.TextStyle(
                  fontSize: bodyFontSize - 1,
                  color: secondaryColor != null ? _convertColor(secondaryColor) : PdfColors.grey700,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildLanguagesSection(List<LanguageModel> languages, ResumeTemplateModel? template) {
    final bodyFontSize = template?.style.bodyFontSize ?? 14.0;
    final primaryColor = template?.style.primaryColor;
    final accentColor = template?.style.accentColor;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Section title with primary color
        pw.Text(
          'Languages',
          style: pw.TextStyle(
            fontSize: bodyFontSize + 2,
            fontWeight: pw.FontWeight.bold,
            color: primaryColor != null ? _convertColor(primaryColor) : PdfColors.black,
          ),
        ),
        // Add accent line under section title if accent color is available
        if (accentColor != null) ...[
          pw.SizedBox(height: 4),
          pw.Container(
            height: 1,
            width: 50,
            color: _convertColor(accentColor),
          ),
        ],
        pw.SizedBox(height: 8),
        pw.Text(
          languages.map((lang) => '${lang.language} (${lang.proficiency})').join(', '),
          style: pw.TextStyle(
            fontSize: bodyFontSize,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  String _formatDateRange(DateTime startDate, DateTime? endDate, bool isCurrent) {
    final startFormatted = DateFormat('MMM yyyy').format(startDate);
    if (isCurrent) {
      return '$startFormatted - Present';
    } else if (endDate != null) {
      final endFormatted = DateFormat('MMM yyyy').format(endDate);
      return '$startFormatted - $endFormatted';
    } else {
      return startFormatted;
    }
  }

  Future<void> _savePdf(pw.Document pdf, String fileName) async {
    try {
      // For mobile platforms, use the printing package to save/share
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: '${fileName}_resume.pdf',
      );
    } catch (e) {
      // Fallback: save to documents directory
      final output = await getApplicationDocumentsDirectory();
      final file = File('${output.path}/${fileName}_resume.pdf');
      await file.writeAsBytes(await pdf.save());
    }
  }
}
