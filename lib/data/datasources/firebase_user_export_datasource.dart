import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import '../models/user_export_count_model.dart';

class FirebaseUserExportDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _firebaseAuth;

  static const String _collectionName = 'user_export_counts';

  FirebaseUserExportDataSource(
    this._firestore,
    this._firebaseAuth,
  );

  /// Get user export count data
  Future<UserExportCountModel?> getUserExportCount(String userId) async {
    try {
      debugPrint('DEBUG: Getting export count for user: $userId');
      
      final doc = await _firestore
          .collection(_collectionName)
          .doc(userId)
          .get();

      if (doc.exists && doc.data() != null) {
        final data = doc.data()!;
        debugPrint('DEBUG: Found export count data: $data');
        return UserExportCountModel.fromJson(data);
      }

      debugPrint('DEBUG: No export count data found, creating new record');
      // Create new record if doesn't exist
      final newRecord = UserExportCountModel(
        userId: userId,
        exportCount: 0,
        lastExportDate: DateTime.now(),
        isPremiumUser: false,
      );
      
      await _saveUserExportCount(newRecord);
      return newRecord;
    } catch (e) {
      debugPrint('DEBUG: Error getting user export count: $e');
      throw Exception('Failed to get user export count: $e');
    }
  }

  /// Save or update user export count
  Future<void> saveUserExportCount(UserExportCountModel exportCount) async {
    try {
      await _saveUserExportCount(exportCount);
    } catch (e) {
      debugPrint('DEBUG: Error saving user export count: $e');
      throw Exception('Failed to save user export count: $e');
    }
  }

  /// Internal method to save export count data
  Future<void> _saveUserExportCount(UserExportCountModel exportCount) async {
    debugPrint('DEBUG: Saving export count: ${exportCount.toJson()}');
    
    await _firestore
        .collection(_collectionName)
        .doc(exportCount.userId)
        .set(exportCount.toJson(), SetOptions(merge: true));
  }

  /// Increment user export count
  Future<UserExportCountModel> incrementExportCount(String userId) async {
    try {
      debugPrint('DEBUG: Incrementing export count for user: $userId');
      
      final currentData = await getUserExportCount(userId);
      if (currentData == null) {
        throw Exception('User export data not found');
      }

      final updatedData = currentData.incrementExportCount();
      await _saveUserExportCount(updatedData);
      
      debugPrint('DEBUG: Export count incremented to: ${updatedData.exportCount}');
      return updatedData;
    } catch (e) {
      debugPrint('DEBUG: Error incrementing export count: $e');
      throw Exception('Failed to increment export count: $e');
    }
  }

  /// Reset user export count
  Future<UserExportCountModel> resetExportCount(String userId) async {
    try {
      debugPrint('DEBUG: Resetting export count for user: $userId');
      
      final currentData = await getUserExportCount(userId);
      if (currentData == null) {
        throw Exception('User export data not found');
      }

      final resetData = currentData.resetExportCount();
      await _saveUserExportCount(resetData);
      
      debugPrint('DEBUG: Export count reset for user: $userId');
      return resetData;
    } catch (e) {
      debugPrint('DEBUG: Error resetting export count: $e');
      throw Exception('Failed to reset export count: $e');
    }
  }

  /// Update user premium status
  Future<void> updatePremiumStatus(String userId, bool isPremium) async {
    try {
      debugPrint('DEBUG: Updating premium status for user: $userId to $isPremium');
      
      final currentData = await getUserExportCount(userId);
      if (currentData == null) {
        throw Exception('User export data not found');
      }

      final updatedData = currentData.copyWith(isPremiumUser: isPremium);
      await _saveUserExportCount(updatedData);
      
      debugPrint('DEBUG: Premium status updated for user: $userId');
    } catch (e) {
      debugPrint('DEBUG: Error updating premium status: $e');
      throw Exception('Failed to update premium status: $e');
    }
  }

  /// Get current authenticated user's export count
  Future<UserExportCountModel?> getCurrentUserExportCount() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }
    
    return await getUserExportCount(user.uid);
  }

  /// Check if user needs export count reset (monthly reset)
  Future<bool> checkAndResetIfNeeded(String userId) async {
    try {
      final currentData = await getUserExportCount(userId);
      if (currentData == null) return false;

      if (currentData.shouldReset()) {
        await resetExportCount(userId);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('DEBUG: Error checking reset: $e');
      return false;
    }
  }

  /// Delete user export count data
  Future<void> deleteUserExportCount(String userId) async {
    try {
      debugPrint('DEBUG: Deleting export count data for user: $userId');
      
      await _firestore
          .collection(_collectionName)
          .doc(userId)
          .delete();
          
      debugPrint('DEBUG: Export count data deleted for user: $userId');
    } catch (e) {
      debugPrint('DEBUG: Error deleting export count: $e');
      throw Exception('Failed to delete user export count: $e');
    }
  }
}
