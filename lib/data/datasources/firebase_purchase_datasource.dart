import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import '../models/purchase_model.dart';

class FirebasePurchaseDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _firebaseAuth;

  static const String _purchasesCollection = 'purchases';
  static const String _subscriptionsCollection = 'user_subscriptions';

  FirebasePurchaseDataSource(
    this._firestore,
    this._firebaseAuth,
  );

  /// Save a completed purchase to Firestore
  Future<void> savePurchase(PurchaseModel purchase) async {
    try {
      debugPrint('DEBUG: Purchase - Saving purchase: ${purchase.toJson()}');
      
      await _firestore
          .collection(_purchasesCollection)
          .doc(purchase.purchaseId)
          .set(purchase.toJson(), SetOptions(merge: true));
          
      debugPrint('DEBUG: Purchase - Purchase saved successfully');
    } catch (e) {
      debugPrint('DEBUG: Purchase - Error saving purchase: $e');
      throw Exception('Failed to save purchase: $e');
    }
  }

  /// Get user's purchase history
  Future<List<PurchaseModel>> getUserPurchases(String userId) async {
    try {
      debugPrint('DEBUG: Purchase - Getting purchases for user: $userId');
      
      final querySnapshot = await _firestore
          .collection(_purchasesCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('purchaseDate', descending: true)
          .get();

      final purchases = querySnapshot.docs
          .map((doc) => PurchaseModel.fromJson(doc.data()))
          .toList();

      debugPrint('DEBUG: Purchase - Found ${purchases.length} purchases');
      return purchases;
    } catch (e) {
      debugPrint('DEBUG: Purchase - Error getting purchases: $e');
      throw Exception('Failed to get purchases: $e');
    }
  }

  /// Save user subscription status
  Future<void> saveUserSubscription(UserSubscriptionModel subscription) async {
    try {
      debugPrint('DEBUG: Purchase - Saving subscription: ${subscription.toJson()}');
      
      await _firestore
          .collection(_subscriptionsCollection)
          .doc(subscription.userId)
          .set(subscription.toJson(), SetOptions(merge: true));
          
      debugPrint('DEBUG: Purchase - Subscription saved successfully');
    } catch (e) {
      debugPrint('DEBUG: Purchase - Error saving subscription: $e');
      throw Exception('Failed to save subscription: $e');
    }
  }

  /// Get user's subscription status
  Future<UserSubscriptionModel?> getUserSubscription(String userId) async {
    try {
      debugPrint('DEBUG: Purchase - Getting subscription for user: $userId');
      
      final doc = await _firestore
          .collection(_subscriptionsCollection)
          .doc(userId)
          .get();

      if (doc.exists && doc.data() != null) {
        final subscription = UserSubscriptionModel.fromJson(doc.data()!);
        debugPrint('DEBUG: Purchase - Found subscription: ${subscription.subscriptionType}');
        return subscription;
      }

      debugPrint('DEBUG: Purchase - No subscription found');
      return null;
    } catch (e) {
      debugPrint('DEBUG: Purchase - Error getting subscription: $e');
      throw Exception('Failed to get subscription: $e');
    }
  }

  /// Update user subscription status
  Future<void> updateUserSubscription({
    required String userId,
    required SubscriptionType subscriptionType,
    required bool isActive,
    String? productId,
    DateTime? subscriptionEnd,
  }) async {
    try {
      debugPrint('DEBUG: Purchase - Updating subscription for user: $userId');
      
      final currentSubscription = await getUserSubscription(userId);
      
      final updatedSubscription = (currentSubscription ?? UserSubscriptionModel(
        userId: userId,
        subscriptionType: SubscriptionType.none,
        isActive: false,
        purchaseIds: [],
      )).copyWith(
        subscriptionType: subscriptionType,
        isActive: isActive,
        productId: productId,
        subscriptionStart: currentSubscription?.subscriptionStart ?? DateTime.now(),
        subscriptionEnd: subscriptionEnd,
      );

      await saveUserSubscription(updatedSubscription);
      debugPrint('DEBUG: Purchase - Subscription updated successfully');
    } catch (e) {
      debugPrint('DEBUG: Purchase - Error updating subscription: $e');
      throw Exception('Failed to update subscription: $e');
    }
  }

  /// Check if user has active premium subscription
  Future<bool> hasActivePremiumSubscription(String userId) async {
    try {
      final subscription = await getUserSubscription(userId);
      
      if (subscription == null || !subscription.isActive) {
        return false;
      }

      // Check if subscription is still valid (not expired)
      if (subscription.subscriptionEnd != null) {
        final now = DateTime.now();
        if (now.isAfter(subscription.subscriptionEnd!)) {
          // Subscription expired, update status
          await updateUserSubscription(
            userId: userId,
            subscriptionType: subscription.subscriptionType,
            isActive: false,
          );
          return false;
        }
      }

      return subscription.isPremium;
    } catch (e) {
      debugPrint('DEBUG: Purchase - Error checking premium status: $e');
      return false;
    }
  }

  /// Get current user's subscription status
  Future<UserSubscriptionModel?> getCurrentUserSubscription() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }
    
    return await getUserSubscription(user.uid);
  }

  /// Process a successful purchase and update subscription
  Future<void> processPurchase(PurchaseModel purchase) async {
    try {
      debugPrint('DEBUG: Purchase - Processing purchase: ${purchase.productId}');
      
      // Save the purchase record
      await savePurchase(purchase);

      // Determine subscription type and duration based on product ID
      SubscriptionType subscriptionType;
      DateTime? subscriptionEnd;

      switch (purchase.productId) {
        case 'premium_monthly':
          subscriptionType = SubscriptionType.premium;
          subscriptionEnd = DateTime.now().add(const Duration(days: 30));
          break;
        case 'premium_yearly':
          subscriptionType = SubscriptionType.premium;
          subscriptionEnd = DateTime.now().add(const Duration(days: 365));
          break;
        case 'premium_lifetime':
          subscriptionType = SubscriptionType.premium;
          subscriptionEnd = null; // Lifetime subscription
          break;
        default:
          subscriptionType = SubscriptionType.premium;
          subscriptionEnd = DateTime.now().add(const Duration(days: 30));
      }

      // Update user subscription
      await updateUserSubscription(
        userId: purchase.userId,
        subscriptionType: subscriptionType,
        isActive: true,
        productId: purchase.productId,
        subscriptionEnd: subscriptionEnd,
      );

      debugPrint('DEBUG: Purchase - Purchase processed successfully');
    } catch (e) {
      debugPrint('DEBUG: Purchase - Error processing purchase: $e');
      throw Exception('Failed to process purchase: $e');
    }
  }

  /// Cancel user subscription
  Future<void> cancelSubscription(String userId) async {
    try {
      debugPrint('DEBUG: Purchase - Canceling subscription for user: $userId');
      
      final subscription = await getUserSubscription(userId);
      if (subscription != null) {
        await updateUserSubscription(
          userId: userId,
          subscriptionType: subscription.subscriptionType,
          isActive: false,
        );
      }
      
      debugPrint('DEBUG: Purchase - Subscription canceled');
    } catch (e) {
      debugPrint('DEBUG: Purchase - Error canceling subscription: $e');
      throw Exception('Failed to cancel subscription: $e');
    }
  }
}
