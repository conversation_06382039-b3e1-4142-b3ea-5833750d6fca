import 'package:flutter/material.dart';

import '../../domain/repositories/user_export_repository.dart';
import '../models/user_export_count_model.dart';
import '../datasources/firebase_user_export_datasource.dart';

class UserExportRepositoryImpl implements UserExportRepository {
  final FirebaseUserExportDataSource _dataSource;

  UserExportRepositoryImpl(this._dataSource);

  @override
  Future<UserExportCountModel?> getUserExportCount(String userId) async {
    return await _dataSource.getUserExportCount(userId);
  }

  @override
  Future<UserExportCountModel?> getCurrentUserExportCount() async {
    return await _dataSource.getCurrentUserExportCount();
  }

  @override
  Future<void> saveUserExportCount(UserExportCountModel exportCount) async {
    return await _dataSource.saveUserExportCount(exportCount);
  }

  @override
  Future<UserExportCountModel> incrementExportCount(String userId) async {
    return await _dataSource.incrementExportCount(userId);
  }

  @override
  Future<UserExportCountModel> resetExportCount(String userId) async {
    return await _dataSource.resetExportCount(userId);
  }

  @override
  Future<void> updatePremiumStatus(String userId, bool isPremium) async {
    return await _dataSource.updatePremiumStatus(userId, isPremium);
  }

  @override
  Future<bool> checkAndResetIfNeeded(String userId) async {
    return await _dataSource.checkAndResetIfNeeded(userId);
  }

  @override
  Future<void> deleteUserExportCount(String userId) async {
    return await _dataSource.deleteUserExportCount(userId);
  }

  @override
  Future<bool> canUserExport(String userId, int maxFreeExports, int maxPremiumExports) async {
    try {
      debugPrint('DEBUG: Checking if user $userId can export');
      debugPrint('DEBUG: Max free exports: $maxFreeExports, Max premium exports: $maxPremiumExports');
      
      // Check and reset if needed first
      await checkAndResetIfNeeded(userId);
      
      final exportData = await getUserExportCount(userId);
      if (exportData == null) {
        debugPrint('DEBUG: No export data found, user can export');
        return true;
      }

      debugPrint('DEBUG: Current export count: ${exportData.exportCount}, Is premium: ${exportData.isPremiumUser}');

      // Premium users have unlimited exports if maxPremiumExports is -1
      if (exportData.isPremiumUser && maxPremiumExports == -1) {
        debugPrint('DEBUG: Premium user with unlimited exports');
        return true;
      }

      // Check limits based on user type
      final maxAllowed = exportData.isPremiumUser ? maxPremiumExports : maxFreeExports;
      final canExport = exportData.exportCount < maxAllowed;
      
      debugPrint('DEBUG: Max allowed: $maxAllowed, Can export: $canExport');
      return canExport;
    } catch (e) {
      debugPrint('DEBUG: Error checking export permission: $e');
      // In case of error, allow export to avoid blocking users
      return true;
    }
  }

  @override
  Future<void> validateExportAttempt(String userId, int maxFreeExports, int maxPremiumExports) async {
    debugPrint('DEBUG: Validating export attempt for user: $userId');
    
    // Check and reset if needed first
    await checkAndResetIfNeeded(userId);
    
    final exportData = await getUserExportCount(userId);
    if (exportData == null) {
      debugPrint('DEBUG: No export data found, validation passed');
      return;
    }

    debugPrint('DEBUG: Validating - Current count: ${exportData.exportCount}, Is premium: ${exportData.isPremiumUser}');

    // Premium users have unlimited exports if maxPremiumExports is -1
    if (exportData.isPremiumUser && maxPremiumExports == -1) {
      debugPrint('DEBUG: Premium user with unlimited exports, validation passed');
      return;
    }

    // Check limits based on user type
    final maxAllowed = exportData.isPremiumUser ? maxPremiumExports : maxFreeExports;
    
    if (exportData.exportCount >= maxAllowed) {
      debugPrint('DEBUG: Export limit exceeded - Count: ${exportData.exportCount}, Max: $maxAllowed');
      throw ExportLimitExceededException(
        message: exportData.isPremiumUser 
            ? 'You have reached your premium export limit of $maxAllowed exports.'
            : 'You have reached your free export limit of $maxAllowed exports. Upgrade to premium for unlimited exports.',
        currentCount: exportData.exportCount,
        maxAllowed: maxAllowed,
        isPremiumUser: exportData.isPremiumUser,
      );
    }
    
    debugPrint('DEBUG: Export validation passed');
  }
}
