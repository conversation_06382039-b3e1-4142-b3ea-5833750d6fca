import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/repositories/resume_repository.dart';
import '../../domain/repositories/user_export_repository.dart';
import '../../domain/repositories/remote_config_repository.dart';
import '../models/simple_resume_model.dart';
import '../models/resume_template_model.dart';
import '../datasources/firebase_resume_datasource.dart';
import '../datasources/local_resume_datasource.dart';
import '../datasources/pdf_export_datasource.dart';

class ResumeRepositoryImpl implements ResumeRepository {
  final FirebaseResumeDataSource _firebaseDataSource;
  final LocalResumeDataSource _localDataSource;
  final PdfExportDataSource _pdfExportDataSource;
  final UserExportRepository? _userExportRepository;
  final RemoteConfigRepository? _remoteConfigRepository;

  ResumeRepositoryImpl(
    this._firebaseDataSource,
    this._localDataSource,
    this._pdfExportDataSource, {
    UserExportRepository? userExportRepository,
    RemoteConfigRepository? remoteConfigRepository,
  }) : _userExportRepository = userExportRepository,
       _remoteConfigRepository = remoteConfigRepository;

  @override
  Future<ResumeModel> getResume(String resumeId) async {
    try {
      return await _firebaseDataSource.getResume(resumeId);
    } catch (e) {
      // Fallback to local storage if Firebase fails
      final localResume = await _localDataSource.getResume();
      if (localResume != null && localResume.id == resumeId) {
        return localResume;
      }
      rethrow;
    }
  }

  @override
  Future<List<ResumeModel>> getUserResumes(String userId) async {
    try {
      return await _firebaseDataSource.getUserResumes(userId);
    } catch (e) {
      // For test user, rethrow the error so we can debug what's happening
      const testUserId = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';
      if (userId == testUserId) {
        print('Firebase error for test user: $e');
        rethrow; // Let the error bubble up so we can see what's wrong
      }

      // Fallback to local storage if Firebase fails for other users
      return await _localDataSource.getAllResumes();
    }
  }

  @override
  Future<void> saveResume(ResumeModel resume) async {
    // Always save locally first for immediate feedback
    await _localDataSource.saveResume(resume);

    try {
      // Then try to save to Firebase
      await _firebaseDataSource.saveResume(resume);
    } catch (e) {
      // If Firebase fails, the local save still succeeded
      // The data will sync when connection is restored
      throw Exception('Saved locally, but failed to sync with cloud: $e');
    }
  }

  @override
  Future<void> deleteResume(String resumeId) async {
    await _firebaseDataSource.deleteResume(resumeId);
  }

  @override
  Future<ResumeModel> duplicateResume(String resumeId) async {
    return await _firebaseDataSource.duplicateResume(resumeId);
  }

  @override
  Future<void> exportToPdf(ResumeModel resume, {ResumeTemplateModel? template}) async {
    return await _pdfExportDataSource.exportToPdf(resume, template: template);
  }

  @override
  Future<void> exportToPdfWithLimitCheck(ResumeModel resume, {ResumeTemplateModel? template}) async {
    // If export tracking is not available, fall back to regular export
    final userExportRepo = _userExportRepository;
    final remoteConfigRepo = _remoteConfigRepository;

    if (userExportRepo == null || remoteConfigRepo == null) {
      return await exportToPdf(resume, template: template);
    }

    try {
      // Get remote config for export limits
      final config = remoteConfigRepo.getConfig();

      // Skip limit check if export limits are disabled
      if (!config.enableExportLimits) {
        return await exportToPdf(resume, template: template);
      }

      // Get current user ID
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Validate export attempt
      await userExportRepo.validateExportAttempt(
        user.uid,
        config.maxFreeExports,
        config.maxPremiumExports,
      );

      // If validation passes, perform the export
      await exportToPdf(resume, template: template);

      // Increment export count after successful export
      await userExportRepo.incrementExportCount(user.uid);

    } catch (e) {
      // Re-throw the exception to be handled by the calling code
      rethrow;
    }
  }

  @override
  Future<String> shareResume(String resumeId) async {
    return await _firebaseDataSource.shareResume(resumeId);
  }

  @override
  Future<void> saveResumeLocally(ResumeModel resume) async {
    return await _localDataSource.saveResume(resume);
  }

  @override
  Future<ResumeModel?> getLocalResume() async {
    return await _localDataSource.getResume();
  }
}
