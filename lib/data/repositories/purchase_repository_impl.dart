import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import '../../domain/repositories/purchase_repository.dart';
import '../../core/services/in_app_purchase_service.dart';
import '../../core/services/purchase_availability_service.dart';
import '../datasources/firebase_purchase_datasource.dart';
import '../models/purchase_model.dart';

class PurchaseRepositoryImpl implements PurchaseRepository {
  final InAppPurchaseService _purchaseService;
  final FirebasePurchaseDataSource _firebaseDataSource;
  final PurchaseAvailabilityService _purchaseAvailabilityService;

  PurchaseRepositoryImpl(
    this._purchaseService,
    this._firebaseDataSource,
    this._purchaseAvailabilityService,
  ) {
    // Inject the availability service into the purchase service
    _purchaseService.setPurchaseAvailabilityService(_purchaseAvailabilityService);

    // Listen to purchase events and process them
    _purchaseService.purchaseStream.listen(_handlePurchaseEvent);
  }

  @override
  Future<bool> initialize() async {
    return await _purchaseService.initialize();
  }

  @override
  Future<List<PurchaseProductModel>> getAvailableProducts() async {
    return _purchaseService.availableProducts;
  }

  @override
  Future<bool> purchaseProduct(String productId) async {
    return await _purchaseService.purchaseProduct(productId);
  }

  @override
  Future<void> restorePurchases() async {
    return await _purchaseService.restorePurchases();
  }

  @override
  Future<void> savePurchase(PurchaseModel purchase) async {
    return await _firebaseDataSource.savePurchase(purchase);
  }

  @override
  Future<List<PurchaseModel>> getUserPurchases(String userId) async {
    return await _firebaseDataSource.getUserPurchases(userId);
  }

  @override
  Future<UserSubscriptionModel?> getUserSubscription(String userId) async {
    return await _firebaseDataSource.getUserSubscription(userId);
  }

  @override
  Future<bool> hasActivePremiumSubscription(String userId) async {
    return await _firebaseDataSource.hasActivePremiumSubscription(userId);
  }

  @override
  Future<void> updateUserSubscription({
    required String userId,
    required SubscriptionType subscriptionType,
    required bool isActive,
    String? productId,
    DateTime? subscriptionEnd,
  }) async {
    return await _firebaseDataSource.updateUserSubscription(
      userId: userId,
      subscriptionType: subscriptionType,
      isActive: isActive,
      productId: productId,
      subscriptionEnd: subscriptionEnd,
    );
  }

  @override
  Future<void> processPurchase(PurchaseModel purchase) async {
    return await _firebaseDataSource.processPurchase(purchase);
  }

  @override
  Future<void> cancelSubscription(String userId) async {
    return await _firebaseDataSource.cancelSubscription(userId);
  }

  @override
  Future<UserSubscriptionModel?> getCurrentUserSubscription() async {
    return await _firebaseDataSource.getCurrentUserSubscription();
  }

  @override
  Stream<PurchaseModel> get purchaseStream => _purchaseService.purchaseStream;

  @override
  Stream<List<PurchaseProductModel>> get productsStream => _purchaseService.productsStream;

  @override
  Stream<String> get errorStream => _purchaseService.errorStream;

  /// Handle purchase events from the service
  void _handlePurchaseEvent(PurchaseModel purchase) async {
    try {
      debugPrint('DEBUG: Purchase Repository - Handling purchase event: ${purchase.productId}');
      
      // Get current user
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        debugPrint('DEBUG: Purchase Repository - No authenticated user');
        return;
      }

      // Update purchase with user ID
      final updatedPurchase = purchase.copyWith(userId: user.uid);
      
      // Process the purchase
      await processPurchase(updatedPurchase);
      
      debugPrint('DEBUG: Purchase Repository - Purchase processed successfully');
    } catch (e) {
      debugPrint('DEBUG: Purchase Repository - Error handling purchase: $e');
    }
  }
}
