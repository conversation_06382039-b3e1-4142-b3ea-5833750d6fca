import 'package:equatable/equatable.dart';

class CoverLetterModel extends Equatable {
  final String id;
  final String userId;
  final String resumeId;
  final String companyName;
  final String positionTitle;
  final String jobDescription;
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CoverLetterModel({
    required this.id,
    required this.userId,
    required this.resumeId,
    required this.companyName,
    required this.positionTitle,
    required this.jobDescription,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CoverLetterModel.fromJson(Map<String, dynamic> json) {
    return CoverLetterModel(
      id: json['id'] as String? ?? '',
      userId: json['userId'] as String? ?? '',
      resumeId: json['resumeId'] as String? ?? '',
      companyName: json['companyName'] as String? ?? '',
      positionTitle: json['positionTitle'] as String? ?? '',
      jobDescription: json['jobDescription'] as String? ?? '',
      content: json['content'] as String? ?? '',
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'] as String) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String) 
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'resumeId': resumeId,
      'companyName': companyName,
      'positionTitle': positionTitle,
      'jobDescription': jobDescription,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  CoverLetterModel copyWith({
    String? id,
    String? userId,
    String? resumeId,
    String? companyName,
    String? positionTitle,
    String? jobDescription,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CoverLetterModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      resumeId: resumeId ?? this.resumeId,
      companyName: companyName ?? this.companyName,
      positionTitle: positionTitle ?? this.positionTitle,
      jobDescription: jobDescription ?? this.jobDescription,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        resumeId,
        companyName,
        positionTitle,
        jobDescription,
        content,
        createdAt,
        updatedAt,
      ];

  /// Get a display name for the cover letter
  String get displayName {
    if (companyName.isNotEmpty && positionTitle.isNotEmpty) {
      return '$positionTitle at $companyName';
    } else if (companyName.isNotEmpty) {
      return companyName;
    } else if (positionTitle.isNotEmpty) {
      return positionTitle;
    } else {
      return 'Cover Letter';
    }
  }

  /// Get a short preview of the content
  String get preview {
    if (content.length <= 100) {
      return content;
    }
    return '${content.substring(0, 100)}...';
  }
}
