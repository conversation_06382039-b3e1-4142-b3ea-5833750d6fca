import 'package:equatable/equatable.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

/// Model for representing a product available for purchase
class PurchaseProductModel extends Equatable {
  final String id;
  final String title;
  final String description;
  final String price;
  final String currencyCode;
  final double rawPrice;
  final bool isAvailable;

  const PurchaseProductModel({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.currencyCode,
    required this.rawPrice,
    required this.isAvailable,
  });

  factory PurchaseProductModel.fromProductDetails(ProductDetails product) {
    return PurchaseProductModel(
      id: product.id,
      title: product.title,
      description: product.description,
      price: product.price,
      currencyCode: product.currencyCode,
      rawPrice: product.rawPrice,
      isAvailable: true,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        price,
        currencyCode,
        rawPrice,
        isAvailable,
      ];
}

/// Model for representing a completed purchase
class PurchaseModel extends Equatable {
  final String purchaseId;
  final String productId;
  final String userId;
  final DateTime purchaseDate;
  final bool isVerified;
  final String? transactionId;
  final PurchaseStatus status;

  const PurchaseModel({
    required this.purchaseId,
    required this.productId,
    required this.userId,
    required this.purchaseDate,
    required this.isVerified,
    this.transactionId,
    required this.status,
  });

  factory PurchaseModel.fromPurchaseDetails(
    PurchaseDetails purchase,
    String userId,
  ) {
    return PurchaseModel(
      purchaseId: purchase.purchaseID ?? '',
      productId: purchase.productID,
      userId: userId,
      purchaseDate: DateTime.now(),
      isVerified: purchase.status == PurchaseStatus.purchased,
      transactionId: purchase.transactionDate,
      status: purchase.status,
    );
  }

  factory PurchaseModel.fromJson(Map<String, dynamic> json) {
    return PurchaseModel(
      purchaseId: json['purchaseId'] as String,
      productId: json['productId'] as String,
      userId: json['userId'] as String,
      purchaseDate: DateTime.parse(json['purchaseDate'] as String),
      isVerified: json['isVerified'] as bool,
      transactionId: json['transactionId'] as String?,
      status: PurchaseStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => PurchaseStatus.error,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'purchaseId': purchaseId,
      'productId': productId,
      'userId': userId,
      'purchaseDate': purchaseDate.toIso8601String(),
      'isVerified': isVerified,
      'transactionId': transactionId,
      'status': status.toString(),
    };
  }

  PurchaseModel copyWith({
    String? purchaseId,
    String? productId,
    String? userId,
    DateTime? purchaseDate,
    bool? isVerified,
    String? transactionId,
    PurchaseStatus? status,
  }) {
    return PurchaseModel(
      purchaseId: purchaseId ?? this.purchaseId,
      productId: productId ?? this.productId,
      userId: userId ?? this.userId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      isVerified: isVerified ?? this.isVerified,
      transactionId: transactionId ?? this.transactionId,
      status: status ?? this.status,
    );
  }

  @override
  List<Object?> get props => [
        purchaseId,
        productId,
        userId,
        purchaseDate,
        isVerified,
        transactionId,
        status,
      ];
}

/// Enum for different subscription types
enum SubscriptionType {
  none,
  premium,
  premiumPlus,
}

/// Model for user subscription status
class UserSubscriptionModel extends Equatable {
  final String userId;
  final SubscriptionType subscriptionType;
  final DateTime? subscriptionStart;
  final DateTime? subscriptionEnd;
  final bool isActive;
  final String? productId;
  final List<String> purchaseIds;

  const UserSubscriptionModel({
    required this.userId,
    required this.subscriptionType,
    this.subscriptionStart,
    this.subscriptionEnd,
    required this.isActive,
    this.productId,
    required this.purchaseIds,
  });

  factory UserSubscriptionModel.fromJson(Map<String, dynamic> json) {
    return UserSubscriptionModel(
      userId: json['userId'] as String,
      subscriptionType: SubscriptionType.values.firstWhere(
        (e) => e.toString() == json['subscriptionType'],
        orElse: () => SubscriptionType.none,
      ),
      subscriptionStart: json['subscriptionStart'] != null
          ? DateTime.parse(json['subscriptionStart'] as String)
          : null,
      subscriptionEnd: json['subscriptionEnd'] != null
          ? DateTime.parse(json['subscriptionEnd'] as String)
          : null,
      isActive: json['isActive'] as bool? ?? false,
      productId: json['productId'] as String?,
      purchaseIds: List<String>.from(json['purchaseIds'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'subscriptionType': subscriptionType.toString(),
      'subscriptionStart': subscriptionStart?.toIso8601String(),
      'subscriptionEnd': subscriptionEnd?.toIso8601String(),
      'isActive': isActive,
      'productId': productId,
      'purchaseIds': purchaseIds,
    };
  }

  UserSubscriptionModel copyWith({
    String? userId,
    SubscriptionType? subscriptionType,
    DateTime? subscriptionStart,
    DateTime? subscriptionEnd,
    bool? isActive,
    String? productId,
    List<String>? purchaseIds,
  }) {
    return UserSubscriptionModel(
      userId: userId ?? this.userId,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionStart: subscriptionStart ?? this.subscriptionStart,
      subscriptionEnd: subscriptionEnd ?? this.subscriptionEnd,
      isActive: isActive ?? this.isActive,
      productId: productId ?? this.productId,
      purchaseIds: purchaseIds ?? this.purchaseIds,
    );
  }

  bool get isPremium => isActive && subscriptionType != SubscriptionType.none;

  @override
  List<Object?> get props => [
        userId,
        subscriptionType,
        subscriptionStart,
        subscriptionEnd,
        isActive,
        productId,
        purchaseIds,
      ];
}
