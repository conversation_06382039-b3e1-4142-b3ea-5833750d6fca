import 'package:equatable/equatable.dart';

/// Model for tracking user PDF export counts
class UserExportCountModel extends Equatable {
  final String userId;
  final int exportCount;
  final DateTime lastExportDate;
  final DateTime? resetDate;
  final bool isPremiumUser;

  const UserExportCountModel({
    required this.userId,
    required this.exportCount,
    required this.lastExportDate,
    this.resetDate,
    required this.isPremiumUser,
  });

  factory UserExportCountModel.fromJson(Map<String, dynamic> json) {
    return UserExportCountModel(
      userId: json['userId'] as String,
      exportCount: json['exportCount'] as int? ?? 0,
      lastExportDate: DateTime.parse(json['lastExportDate'] as String),
      resetDate: json['resetDate'] != null 
          ? DateTime.parse(json['resetDate'] as String) 
          : null,
      isPremiumUser: json['isPremiumUser'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'exportCount': exportCount,
      'lastExportDate': lastExportDate.toIso8601String(),
      'resetDate': resetDate?.toIso8601String(),
      'isPremiumUser': isPremiumUser,
    };
  }

  UserExportCountModel copyWith({
    String? userId,
    int? exportCount,
    DateTime? lastExportDate,
    DateTime? resetDate,
    bool? isPremiumUser,
  }) {
    return UserExportCountModel(
      userId: userId ?? this.userId,
      exportCount: exportCount ?? this.exportCount,
      lastExportDate: lastExportDate ?? this.lastExportDate,
      resetDate: resetDate ?? this.resetDate,
      isPremiumUser: isPremiumUser ?? this.isPremiumUser,
    );
  }

  /// Check if the export count should be reset (e.g., monthly reset)
  bool shouldReset({Duration resetPeriod = const Duration(days: 30)}) {
    if (resetDate == null) return false;
    return DateTime.now().isAfter(resetDate!.add(resetPeriod));
  }

  /// Create a new instance with incremented export count
  UserExportCountModel incrementExportCount() {
    return copyWith(
      exportCount: exportCount + 1,
      lastExportDate: DateTime.now(),
    );
  }

  /// Create a new instance with reset export count
  UserExportCountModel resetExportCount() {
    return copyWith(
      exportCount: 0,
      resetDate: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        userId,
        exportCount,
        lastExportDate,
        resetDate,
        isPremiumUser,
      ];
}

/// Exception thrown when user exceeds export limit
class ExportLimitExceededException implements Exception {
  final String message;
  final int currentCount;
  final int maxAllowed;
  final bool isPremiumUser;

  const ExportLimitExceededException({
    required this.message,
    required this.currentCount,
    required this.maxAllowed,
    required this.isPremiumUser,
  });

  @override
  String toString() => message;
}
