import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../cubits/cover_letter/cover_letter_cubit.dart';
import '../../cubits/resume/resume_cubit.dart';
import '../../widgets/cover_letter/cover_letter_form_widget.dart';
import '../../widgets/cover_letter/cover_letter_display_widget.dart';
import 'resume_selection_page.dart';

class CoverLetterPage extends StatelessWidget {
  const CoverLetterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Cover Letter Generator'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: BlocListener<CoverLetterCubit, CoverLetterState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Theme.of(context).colorScheme.error,
                action: SnackBarAction(
                  label: 'Dismiss',
                  onPressed: () {
                    context.read<CoverLetterCubit>().clearError();
                  },
                ),
              ),
            );
          }
          
          if (state.successMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.successMessage!),
                backgroundColor: Theme.of(context).colorScheme.primary,
                action: SnackBarAction(
                  label: 'Dismiss',
                  onPressed: () {
                    context.read<CoverLetterCubit>().clearSuccess();
                  },
                ),
              ),
            );
          }
        },
        child: BlocBuilder<CoverLetterCubit, CoverLetterState>(
          builder: (context, coverLetterState) {
            return BlocBuilder<ResumeCubit, ResumeState>(
              builder: (context, resumeState) {
                if (resumeState.currentResume == null) {
                  return const _NoResumeWidget();
                }

                if (coverLetterState.currentCoverLetter != null) {
                  return CoverLetterDisplayWidget(
                    coverLetter: coverLetterState.currentCoverLetter!,
                    onEdit: () {
                      context.read<CoverLetterCubit>().clearCurrentCoverLetter();
                    },
                  );
                }

                return CoverLetterFormWidget(
                  resume: resumeState.currentResume!,
                  isGenerating: coverLetterState.isGenerating,
                  onGenerate: (companyName, positionTitle, jobDescription) {
                    context.read<CoverLetterCubit>().generateCoverLetter(
                      resume: resumeState.currentResume!,
                      companyName: companyName,
                      positionTitle: positionTitle,
                      jobDescription: jobDescription,
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }
}

class _NoResumeWidget extends StatelessWidget {
  const _NoResumeWidget();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 80,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 24),
            Text(
              'No Resume Selected',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Please create or select a resume first to generate a cover letter.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            FilledButton.icon(
              onPressed: () {
                Navigator.of(context).pushReplacementNamed('/resume-builder');
              },
              icon: const Icon(Icons.add),
              label: const Text('Create Resume'),
            ),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              onPressed: () {
                print('DEBUG: Navigating to ResumeSelectionPage directly');
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ResumeSelectionPage(),
                  ),
                );
              },
              icon: const Icon(Icons.folder_outlined),
              label: const Text('Select Resume'),
            ),
          ],
        ),
      ),
    );
  }
}
