import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../cubits/auth/auth_cubit.dart';
import '../../cubits/resume/resume_cubit.dart';
import '../../cubits/resume_selection/resume_selection_cubit.dart';
import '../../../data/models/simple_resume_model.dart';
import '../../widgets/common/shimmer_widgets.dart';

class ResumeSelectionPage extends StatefulWidget {
  const ResumeSelectionPage({super.key});

  @override
  State<ResumeSelectionPage> createState() => _ResumeSelectionPageState();
}

class _ResumeSelectionPageState extends State<ResumeSelectionPage> {
  late final ResumeSelectionCubit _resumeSelectionCubit;

  @override
  void initState() {
    super.initState();
    print('DEBUG: ResumeSelectionPage initState called');
    _resumeSelectionCubit = ResumeSelectionCubit(context.read<ResumeCubit>().resumeUseCases);
    _loadUserResumes();
  }

  void _loadUserResumes() {
    final user = context.read<AuthCubit>().state.user;
    if (user != null) {
      _resumeSelectionCubit.loadUserResumes(user.id);
    } else {
      // For testing purposes, load test user data when not authenticated
      const testUserId = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';
      _resumeSelectionCubit.loadUserResumes(testUserId);
    }
  }

  @override
  void dispose() {
    _resumeSelectionCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Resume'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: BlocProvider.value(
        value: _resumeSelectionCubit,
        child: BlocListener<ResumeSelectionCubit, ResumeSelectionState>(
          listener: (context, state) {
            if (state.errorMessage != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.errorMessage!),
                  backgroundColor: Theme.of(context).colorScheme.error,
                  action: SnackBarAction(
                    label: 'Dismiss',
                    onPressed: () {
                      _resumeSelectionCubit.clearError();
                    },
                  ),
                ),
              );
            }
          },
          child: BlocBuilder<ResumeSelectionCubit, ResumeSelectionState>(
            builder: (context, state) {
              if (state.isLoading) {
                return const ResumeListShimmer();
              }

              if (state.resumes.isEmpty) {
                return _buildEmptyState(context);
              }

              return Column(
                children: [
                  Expanded(
                    child: _buildResumesList(context, state.resumes, state.selectedResume),
                  ),
                  _buildContinueButton(context, state.selectedResume),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 80,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 24),
            Text(
              'No Resumes Found',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Create your first resume to generate cover letters.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            FilledButton.icon(
              onPressed: () {
                Navigator.of(context).pushReplacementNamed('/resume-builder');
              },
              icon: const Icon(Icons.add),
              label: const Text('Create Resume'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResumesList(BuildContext context, List<ResumeModel> resumes, ResumeModel? selectedResume) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: resumes.length,
      itemBuilder: (context, index) {
        final resume = resumes[index];
        final isSelected = selectedResume?.id == resume.id;
        return _buildResumeCard(context, resume, isSelected);
      },
    );
  }

  Widget _buildResumeCard(BuildContext context, ResumeModel resume, bool isSelected) {
    final hasContent = resume.personalInfo.firstName.isNotEmpty || 
                      resume.summary.isNotEmpty ||
                      resume.workExperience.isNotEmpty;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 1,
      color: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
      child: InkWell(
        onTap: () {
          _resumeSelectionCubit.selectResume(resume);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                margin: const EdgeInsets.only(left: 12, right: 4),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline,
                    width: 2,
                  ),
                  color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.transparent,
                ),
                child: isSelected
                  ? Icon(
                      Icons.check,
                      size: 12,
                      color: Theme.of(context).colorScheme.onPrimary,
                    )
                  : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            hasContent 
                              ? '${resume.personalInfo.firstName} ${resume.personalInfo.lastName}'.trim()
                              : 'Untitled Resume',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: isSelected ? Theme.of(context).colorScheme.onPrimaryContainer : null,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (!hasContent)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.errorContainer,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Incomplete',
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                color: Theme.of(context).colorScheme.onErrorContainer,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    if (resume.personalInfo.email.isNotEmpty)
                      Text(
                        resume.personalInfo.email,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isSelected 
                            ? Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8)
                            : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: isSelected 
                            ? Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.6)
                            : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Updated ${DateFormat('MMM d, y').format(resume.updatedAt)}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: isSelected 
                              ? Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.6)
                              : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContinueButton(BuildContext context, ResumeModel? selectedResume) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: FilledButton.icon(
          onPressed: selectedResume != null ? () => _continueWithSelectedResume(selectedResume) : null,
          icon: const Icon(Icons.arrow_forward),
          label: const Text(
            'Continue',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          style: FilledButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  void _continueWithSelectedResume(ResumeModel selectedResume) {
    // Load the selected resume into the ResumeCubit
    context.read<ResumeCubit>().loadResumeFromModel(selectedResume);
    
    // Navigate back to the cover letter page
    Navigator.of(context).pop();
  }
}
