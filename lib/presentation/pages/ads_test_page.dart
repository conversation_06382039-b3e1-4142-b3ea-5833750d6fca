import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubits/remote_config/remote_config_cubit.dart';
import '../widgets/ads/banner_ad_widget.dart';
import '../../core/services/ad_service.dart';

class AdsTestPage extends StatefulWidget {
  const AdsTestPage({super.key});

  @override
  State<AdsTestPage> createState() => _AdsTestPageState();
}

class _AdsTestPageState extends State<AdsTestPage> {
  @override
  void initState() {
    super.initState();
    // Load interstitial ad for testing
    AdService.instance.loadInterstitialAd();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ads Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<RemoteConfigCubit>().fetchConfig();
            },
            tooltip: 'Refresh Remote Config',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Remote Config Status
            BlocBuilder<RemoteConfigCubit, RemoteConfigState>(
              builder: (context, state) {
                final cubit = context.read<RemoteConfigCubit>();
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Remote Config Status',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              state is RemoteConfigLoaded ? Icons.check_circle : Icons.error,
                              color: state is RemoteConfigLoaded ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(state is RemoteConfigLoaded ? 'Loaded' : 'Not Loaded'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text('Banner Ads: ${cubit.shouldShowBannerAds() ? "Enabled" : "Disabled"}'),
                        Text('Interstitial Ads: ${cubit.shouldShowInterstitialAds() ? "Enabled" : "Disabled"}'),
                        Text('Premium Templates: ${cubit.isFeatureEnabled("enable_premium_templates") ? "Enabled" : "Disabled"}'),
                      ],
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // Banner Ad Test
            Text(
              'Banner Ad Test',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            const BannerAdWidget(),

            const SizedBox(height: 24),

            // Interstitial Ad Test
            Text(
              'Interstitial Ad Test',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Test interstitial ads by clicking the button below',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        final cubit = context.read<RemoteConfigCubit>();
                        if (cubit.shouldShowInterstitialAds()) {
                          AdService.instance.showInterstitialAd(
                            onAdDismissed: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Interstitial ad dismissed')),
                              );
                            },
                            onAdFailedToShow: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Interstitial ad failed to show')),
                              );
                            },
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Interstitial ads are disabled')),
                          );
                        }
                      },
                      child: const Text('Show Interstitial Ad'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        AdService.instance.loadInterstitialAd(
                          onAdLoaded: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Interstitial ad loaded')),
                            );
                          },
                          onAdFailedToLoad: (error) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('Failed to load ad: $error')),
                            );
                          },
                        );
                      },
                      child: const Text('Load New Interstitial Ad'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Remote Config Controls
            Text(
              'Remote Config Controls',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'To test ad controls:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text('1. Go to Firebase Console → Remote Config'),
                    const Text('2. Toggle show_banner_ads or show_interstitial_ads'),
                    const Text('3. Publish changes'),
                    const Text('4. Tap refresh button above'),
                    const Text('5. Observe ad behavior changes'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        context.read<RemoteConfigCubit>().fetchConfig();
                      },
                      child: const Text('Refresh Remote Config'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Navigation Test
            Text(
              'Navigation Test',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text('Test ads in actual pages:'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(context, '/my-resumes');
                      },
                      child: const Text('Go to My Resumes (Banner Ad)'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(context, '/resume-builder');
                      },
                      child: const Text('Go to Resume Builder (Interstitial Ad)'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
