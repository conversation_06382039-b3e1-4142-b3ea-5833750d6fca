import 'package:flutter/material.dart';

import '../../data/models/purchase_model.dart';
import '../../domain/usecases/purchase_usecases.dart';
import '../../domain/repositories/remote_config_repository.dart';
import '../../injection/injection_container.dart';
import '../widgets/export_status_widget.dart';

class PremiumUpgradePage extends StatefulWidget {
  const PremiumUpgradePage({super.key});

  @override
  State<PremiumUpgradePage> createState() => _PremiumUpgradePageState();
}

class _PremiumUpgradePageState extends State<PremiumUpgradePage> {
  late PurchaseUseCases _purchaseUseCases;
  late RemoteConfigRepository _remoteConfigRepository;
  List<PurchaseProductModel> _products = [];
  bool _isLoading = true;
  String? _error;
  bool _isPurchasing = false;
  bool _purchasesDisabled = false;

  @override
  void initState() {
    super.initState();
    _purchaseUseCases = sl<PurchaseUseCases>();
    _remoteConfigRepository = sl<RemoteConfigRepository>();
    _initializePurchases();
    _listenToPurchaseEvents();
  }

  Future<void> _initializePurchases() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
        _purchasesDisabled = false;
      });

      // Check if purchases are enabled via remote config
      if (!_remoteConfigRepository.areInAppPurchasesEnabled()) {
        setState(() {
          _purchasesDisabled = true;
          _error = 'In-app purchases are temporarily unavailable. Please try again later.';
          _isLoading = false;
        });
        return;
      }

      final initialized = await _purchaseUseCases.initialize();
      if (!initialized) {
        setState(() {
          _error = 'Failed to initialize purchase system';
          _isLoading = false;
        });
        return;
      }

      final products = await _purchaseUseCases.getAvailableProducts();
      debugPrint('DEBUG: Premium Page - Loaded ${products.length} products');
      for (final product in products) {
        debugPrint('DEBUG: Premium Page - Product: ${product.id} - ${product.title} (${product.price})');
      }
      setState(() {
        _products = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _listenToPurchaseEvents() {
    _purchaseUseCases.purchaseStream.listen(
      (purchase) {
        setState(() {
          _isPurchasing = false;
        });
        _showSuccessDialog();
      },
      onError: (error) {
        setState(() {
          _isPurchasing = false;
        });
        _showErrorDialog(error.toString());
      },
    );

    _purchaseUseCases.errorStream.listen(
      (error) {
        setState(() {
          _isPurchasing = false;
        });
        _showErrorDialog(error);
      },
    );
  }

  Future<void> _purchaseProduct(String productId) async {
    try {
      setState(() {
        _isPurchasing = true;
      });

      final success = await _purchaseUseCases.purchaseProduct(productId);
      if (!success) {
        setState(() {
          _isPurchasing = false;
        });
        _showErrorDialog('Failed to start purchase');
      }
    } catch (e) {
      setState(() {
        _isPurchasing = false;
      });
      _showErrorDialog(e.toString());
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Purchase Successful!'),
        content: const Text('Welcome to Premium! You now have unlimited PDF exports.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Purchase Failed'),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upgrade to Premium'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorView()
              : _buildContent(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _purchasesDisabled ? Icons.block : Icons.error_outline,
              size: 64,
              color: _purchasesDisabled ? Colors.orange : Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _purchasesDisabled
                  ? 'Purchases Temporarily Unavailable'
                  : 'Error loading premium options',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            if (_purchasesDisabled) ...[
              const SizedBox(height: 8),
              Text(
                'This may be due to maintenance or policy updates. Please check back later.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _initializePurchases,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current status
          const ExportStatusWidget(showUpgradeButton: false),
          const SizedBox(height: 24),

          // Premium benefits
          _buildBenefitsSection(),
          const SizedBox(height: 24),

          // Products
          _buildProductsSection(),
          const SizedBox(height: 24),

          // Restore purchases button
          _buildRestoreButton(),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber),
                const SizedBox(width: 8),
                Text(
                  'Premium Benefits',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildBenefitItem(Icons.picture_as_pdf, 'Unlimited PDF exports'),
            _buildBenefitItem(Icons.palette, 'Access to premium templates'),
            _buildBenefitItem(Icons.cloud_sync, 'Priority cloud sync'),
            _buildBenefitItem(Icons.support, 'Priority customer support'),
            _buildBenefitItem(Icons.block, 'Ad-free experience'),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(icon, color: Colors.green, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsSection() {
    if (_products.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No premium options available at the moment.'),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Your Plan',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ..._products.map((product) => _buildProductCard(product)),
      ],
    );
  }

  Widget _buildProductCard(PurchaseProductModel product) {
    String subtitle = '';
    Color cardColor = Colors.blue.shade50;
    bool isPopular = false;

    switch (product.id) {
      case 'premium_monthly':
        subtitle = 'Perfect for trying premium features';
        break;
      case 'premium_yearly':
        subtitle = 'Best value - Save 60%';
        cardColor = Colors.amber.shade50;
        isPopular = true;
        break;
      case 'premium_lifetime':
        subtitle = 'One-time payment, lifetime access';
        cardColor = Colors.green.shade50;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        color: cardColor,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              product.title,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (isPopular) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Text(
                                  'POPULAR',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        if (subtitle.isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            subtitle,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        product.price,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isPurchasing ? null : () => _purchaseProduct(product.id),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isPopular ? Colors.amber : Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: _isPurchasing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Subscribe Now',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRestoreButton() {
    return Center(
      child: TextButton(
        onPressed: _isPurchasing ? null : () async {
          try {
            await _purchaseUseCases.restorePurchases();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Purchases restored successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to restore purchases: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
        child: const Text('Restore Previous Purchases'),
      ),
    );
  }
}
