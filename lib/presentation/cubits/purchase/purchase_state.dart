part of 'purchase_cubit.dart';

abstract class PurchaseState extends Equatable {
  const PurchaseState();

  @override
  List<Object?> get props => [];
}

class PurchaseInitial extends PurchaseState {
  const PurchaseInitial();
}

class PurchaseLoading extends PurchaseState {
  const PurchaseLoading();
}

class PurchaseLoaded extends PurchaseState {
  final List<PurchaseProductModel> products;
  final UserSubscriptionModel? subscription;
  final UserExportCountModel? exportStatus;
  final bool isLoading;
  final String? errorMessage;
  final String? successMessage;

  const PurchaseLoaded({
    required this.products,
    this.subscription,
    this.exportStatus,
    this.isLoading = false,
    this.errorMessage,
    this.successMessage,
  });

  PurchaseLoaded copyWith({
    List<PurchaseProductModel>? products,
    UserSubscriptionModel? subscription,
    UserExportCountModel? exportStatus,
    bool? isLoading,
    String? errorMessage,
    String? successMessage,
  }) {
    return PurchaseLoaded(
      products: products ?? this.products,
      subscription: subscription ?? this.subscription,
      exportStatus: exportStatus ?? this.exportStatus,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  bool get isPremiumUser => subscription?.isActive ?? false;
  
  bool get hasExportLimitReached {
    if (exportStatus == null) return false;
    if (isPremiumUser) return false;
    
    // This would need to be calculated with remote config values
    // For now, assume limit of 1 for free users
    return exportStatus!.exportCount >= 1;
  }

  @override
  List<Object?> get props => [
        products,
        subscription,
        exportStatus,
        isLoading,
        errorMessage,
        successMessage,
      ];
}

class PurchaseError extends PurchaseState {
  final String message;

  const PurchaseError(this.message);

  @override
  List<Object?> get props => [message];
}

class PurchaseDisabled extends PurchaseState {
  final String message;
  final String? detailedMessage;

  const PurchaseDisabled(this.message, {this.detailedMessage});

  @override
  List<Object?> get props => [message, detailedMessage];
}
