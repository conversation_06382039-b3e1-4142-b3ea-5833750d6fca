import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../domain/usecases/purchase_usecases.dart';
import '../../../domain/usecases/user_export_usecases.dart';
import '../../../domain/repositories/remote_config_repository.dart';
import '../../../data/models/purchase_model.dart';
import '../../../data/models/user_export_count_model.dart';

part 'purchase_state.dart';

class PurchaseCubit extends Cubit<PurchaseState> {
  final PurchaseUseCases _purchaseUseCases;
  final UserExportUseCases _userExportUseCases;
  final RemoteConfigRepository _remoteConfigRepository;

  PurchaseCubit(
    this._purchaseUseCases,
    this._userExportUseCases,
    this._remoteConfigRepository,
  ) : super(const PurchaseInitial());

  /// Initialize purchase system
  Future<void> initialize() async {
    try {
      emit(const PurchaseLoading());

      // Check if purchases are enabled via remote config
      if (!_remoteConfigRepository.areInAppPurchasesEnabled()) {
        emit(const PurchaseDisabled(
          'In-app purchases are temporarily unavailable. Please try again later.',
          detailedMessage: 'In-app purchases have been disabled via remote configuration.',
        ));
        return;
      }

      final success = await _purchaseUseCases.initialize();
      if (!success) {
        emit(const PurchaseError('Failed to initialize purchase system'));
        return;
      }

      // Listen to purchase events
      _purchaseUseCases.purchaseStream.listen(_handlePurchaseSuccess);
      _purchaseUseCases.errorStream.listen(_handlePurchaseError);

      // Load products and user status
      await _loadPurchaseData();
    } catch (e) {
      debugPrint('DEBUG: Purchase Cubit - Initialize error: $e');
      emit(PurchaseError(e.toString()));
    }
  }

  /// Load purchase data (products and user status)
  Future<void> _loadPurchaseData() async {
    try {
      final products = await _purchaseUseCases.getAvailableProducts();
      final subscription = await _purchaseUseCases.getCurrentUserSubscription();
      final exportStatus = await _userExportUseCases.getCurrentUserExportCount();

      emit(PurchaseLoaded(
        products: products,
        subscription: subscription,
        exportStatus: exportStatus,
        isLoading: false,
      ));
    } catch (e) {
      debugPrint('DEBUG: Purchase Cubit - Load data error: $e');
      emit(PurchaseError(e.toString()));
    }
  }

  /// Purchase a product
  Future<void> purchaseProduct(String productId) async {
    try {
      final currentState = state;
      if (currentState is PurchaseLoaded) {
        emit(currentState.copyWith(isLoading: true));
        
        final success = await _purchaseUseCases.purchaseProduct(productId);
        if (!success) {
          emit(currentState.copyWith(
            isLoading: false,
            errorMessage: 'Failed to start purchase',
          ));
        }
        // Success will be handled by stream listener
      }
    } catch (e) {
      debugPrint('DEBUG: Purchase Cubit - Purchase error: $e');
      final currentState = state;
      if (currentState is PurchaseLoaded) {
        emit(currentState.copyWith(
          isLoading: false,
          errorMessage: e.toString(),
        ));
      }
    }
  }

  /// Restore purchases
  Future<void> restorePurchases() async {
    try {
      final currentState = state;
      if (currentState is PurchaseLoaded) {
        emit(currentState.copyWith(isLoading: true));
        
        await _purchaseUseCases.restorePurchases();
        await _loadPurchaseData(); // Reload data after restore
      }
    } catch (e) {
      debugPrint('DEBUG: Purchase Cubit - Restore error: $e');
      final currentState = state;
      if (currentState is PurchaseLoaded) {
        emit(currentState.copyWith(
          isLoading: false,
          errorMessage: e.toString(),
        ));
      }
    }
  }

  /// Handle successful purchase
  void _handlePurchaseSuccess(PurchaseModel purchase) async {
    try {
      debugPrint('DEBUG: Purchase Cubit - Purchase success: ${purchase.productId}');
      
      // Reload data to reflect new subscription status
      await _loadPurchaseData();
      
      final currentState = state;
      if (currentState is PurchaseLoaded) {
        emit(currentState.copyWith(
          isLoading: false,
          successMessage: 'Purchase successful! Welcome to Premium!',
        ));
      }
    } catch (e) {
      debugPrint('DEBUG: Purchase Cubit - Handle success error: $e');
    }
  }

  /// Handle purchase error
  void _handlePurchaseError(String error) {
    debugPrint('DEBUG: Purchase Cubit - Purchase error: $error');
    
    final currentState = state;
    if (currentState is PurchaseLoaded) {
      emit(currentState.copyWith(
        isLoading: false,
        errorMessage: error,
      ));
    }
  }

  /// Clear messages
  void clearMessages() {
    final currentState = state;
    if (currentState is PurchaseLoaded) {
      emit(currentState.copyWith(
        errorMessage: null,
        successMessage: null,
      ));
    }
  }

  /// Check if user should see upgrade prompt
  bool shouldShowUpgradePrompt() {
    final currentState = state;
    if (currentState is PurchaseLoaded) {
      final exportStatus = currentState.exportStatus;
      final subscription = currentState.subscription;
      
      // Show if user has no active subscription and has export data
      return (subscription == null || !subscription.isActive) && 
             exportStatus != null;
    }
    return false;
  }

  /// Get user's premium status
  bool get isPremiumUser {
    final currentState = state;
    if (currentState is PurchaseLoaded) {
      return currentState.subscription?.isActive ?? false;
    }
    return false;
  }

  /// Refresh purchase data
  Future<void> refresh() async {
    await _loadPurchaseData();
  }
}
