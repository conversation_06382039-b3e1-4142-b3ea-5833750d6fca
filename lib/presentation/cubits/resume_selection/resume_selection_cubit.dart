import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/simple_resume_model.dart';
import '../../../domain/usecases/resume_usecases.dart';

part 'resume_selection_state.dart';

class ResumeSelectionCubit extends Cubit<ResumeSelectionState> {
  final ResumeUseCases _resumeUseCases;

  ResumeSelectionCubit(this._resumeUseCases) : super(const ResumeSelectionState());

  void loadUserResumes(String userId) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final resumes = await _resumeUseCases.getUserResumes(userId);
      emit(state.copyWith(
        resumes: resumes,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void selectResume(ResumeModel resume) {
    emit(state.copyWith(selectedResume: resume));
  }

  void clearSelection() {
    emit(state.copyWith(selectedResume: null));
  }

  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }
}
