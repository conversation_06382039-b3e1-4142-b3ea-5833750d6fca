part of 'resume_selection_cubit.dart';

class ResumeSelectionState extends Equatable {
  final List<ResumeModel> resumes;
  final ResumeModel? selectedResume;
  final bool isLoading;
  final String? errorMessage;

  const ResumeSelectionState({
    this.resumes = const [],
    this.selectedResume,
    this.isLoading = false,
    this.errorMessage,
  });

  ResumeSelectionState copyWith({
    List<ResumeModel>? resumes,
    ResumeModel? selectedResume,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ResumeSelectionState(
      resumes: resumes ?? this.resumes,
      selectedResume: selectedResume,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        resumes,
        selectedResume,
        isLoading,
        errorMessage,
      ];
}
