import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../domain/usecases/remote_config_usecases.dart';
import '../../../data/models/remote_config_model.dart';

part 'remote_config_state.dart';

class RemoteConfigCubit extends Cubit<RemoteConfigState> {
  final InitializeRemoteConfigUseCase _initializeUseCase;
  final FetchRemoteConfigUseCase _fetchUseCase;
  final GetRemoteConfigUseCase _getConfigUseCase;
  final CheckTemplateVisibilityUseCase _checkTemplateVisibilityUseCase;
  final CheckAdDisplayUseCase _checkAdDisplayUseCase;
  final CheckFeatureEnabledUseCase _checkFeatureEnabledUseCase;
  final CheckAppStatusUseCase _checkAppStatusUseCase;
  final GetConfigValueUseCase _getConfigValueUseCase;

  StreamSubscription? _configUpdateSubscription;

  RemoteConfigCubit(
    this._initializeUseCase,
    this._fetchUseCase,
    this._getConfigUseCase,
    this._checkTemplateVisibilityUseCase,
    this._checkAdDisplayUseCase,
    this._checkFeatureEnabledUseCase,
    this._checkAppStatusUseCase,
    this._getConfigValueUseCase,
  ) : super(RemoteConfigInitial());

  /// Initialize Remote Config
  Future<void> initialize() async {
    try {
      emit(RemoteConfigLoading());
      
      await _initializeUseCase();
      final config = _getConfigUseCase();
      
      emit(RemoteConfigLoaded(config));
    } catch (e) {
      emit(RemoteConfigError('Failed to initialize Remote Config: $e'));
    }
  }

  /// Fetch latest config from server
  Future<void> fetchConfig() async {
    try {
      final success = await _fetchUseCase();
      if (success) {
        final config = _getConfigUseCase();
        emit(RemoteConfigLoaded(config));
      } else {
        emit(RemoteConfigError('Failed to fetch remote config'));
      }
    } catch (e) {
      emit(RemoteConfigError('Error fetching config: $e'));
    }
  }

  /// Force fetch config (bypasses minimum fetch interval)
  Future<void> forceFetchConfig() async {
    emit(RemoteConfigLoading());
    try {
      // This will force a fetch regardless of the minimum fetch interval
      final success = await _fetchUseCase();
      if (success) {
        final config = _getConfigUseCase();
        print('Force fetch completed. New config: ${config.toMap()}');
        emit(RemoteConfigLoaded(config));
      } else {
        emit(RemoteConfigError('Failed to force fetch remote config'));
      }
    } catch (e) {
      print('Force fetch error: $e');
      emit(RemoteConfigError('Error force fetching config: $e'));
    }
  }

  /// Get current config
  RemoteConfigModel? getCurrentConfig() {
    final currentState = state;
    if (currentState is RemoteConfigLoaded) {
      return currentState.config;
    }
    return null;
  }

  /// Check if template should be visible
  bool isTemplateVisible(String templateId) {
    try {
      return _checkTemplateVisibilityUseCase(templateId);
    } catch (e) {
      return true; // Default to visible if error
    }
  }

  /// Get ad display configuration
  AdDisplayConfig getAdDisplayConfig() {
    try {
      return _checkAdDisplayUseCase();
    } catch (e) {
      return AdDisplayConfig(
        showBannerAds: true,
        showInterstitialAds: true,
        adFrequency: 3,
      );
    }
  }

  /// Check if feature is enabled
  bool isFeatureEnabled(String featureKey) {
    try {
      final result = _checkFeatureEnabledUseCase(featureKey);
      print('isFeatureEnabled($featureKey): $result');
      return result;
    } catch (e) {
      print('isFeatureEnabled($featureKey) error: $e');
      return false; // Default to disabled if error
    }
  }

  /// Get app status configuration
  AppStatusConfig getAppStatusConfig() {
    try {
      return _checkAppStatusUseCase();
    } catch (e) {
      return AppStatusConfig(
        isMaintenanceMode: false,
        isForceUpdateRequired: false,
        maintenanceMessage: '',
        updateMessage: '',
      );
    }
  }

  /// Get specific config value with type safety
  T getConfigValue<T>(String key, T defaultValue) {
    try {
      return _getConfigValueUseCase<T>(key, defaultValue);
    } catch (e) {
      return defaultValue;
    }
  }

  /// Convenience methods for common checks
  bool shouldShowBannerAds() => getAdDisplayConfig().showBannerAds;
  bool shouldShowInterstitialAds() => getAdDisplayConfig().showInterstitialAds;
  int getAdFrequency() => getAdDisplayConfig().adFrequency;
  bool isMaintenanceMode() => getAppStatusConfig().isMaintenanceMode;
  bool isForceUpdateRequired() => getAppStatusConfig().isForceUpdateRequired;
  bool isTemplatesEnabled() {
    final enabled = isFeatureEnabled('enable_templates');
    print('isTemplatesEnabled called: $enabled');
    return enabled;
  }

  /// Filter visible templates from a list
  List<T> filterVisibleTemplates<T>(
    List<T> templates,
    String Function(T) getTemplateId,
  ) {
    return templates.where((template) {
      final templateId = getTemplateId(template);
      return isTemplateVisible(templateId);
    }).toList();
  }

  @override
  Future<void> close() {
    _configUpdateSubscription?.cancel();
    return super.close();
  }
}
