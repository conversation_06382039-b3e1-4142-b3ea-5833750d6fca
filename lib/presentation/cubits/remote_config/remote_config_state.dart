part of 'remote_config_cubit.dart';

abstract class RemoteConfigState extends Equatable {
  const RemoteConfigState();

  @override
  List<Object?> get props => [];
}

class RemoteConfigInitial extends RemoteConfigState {}

class RemoteConfigLoading extends RemoteConfigState {}

class RemoteConfigLoaded extends RemoteConfigState {
  final RemoteConfigModel config;

  const RemoteConfigLoaded(this.config);

  @override
  List<Object?> get props => [config];
}

class RemoteConfigError extends RemoteConfigState {
  final String message;

  const RemoteConfigError(this.message);

  @override
  List<Object?> get props => [message];
}
