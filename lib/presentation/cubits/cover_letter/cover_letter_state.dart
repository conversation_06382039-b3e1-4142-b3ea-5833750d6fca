part of 'cover_letter_cubit.dart';

class CoverLetterState extends Equatable {
  final List<CoverLetterModel> coverLetters;
  final CoverLetterModel? currentCoverLetter;
  final bool isLoading;
  final bool isGenerating;
  final bool isSaving;
  final String? errorMessage;
  final String? successMessage;

  const CoverLetterState({
    this.coverLetters = const [],
    this.currentCoverLetter,
    this.isLoading = false,
    this.isGenerating = false,
    this.isSaving = false,
    this.errorMessage,
    this.successMessage,
  });

  CoverLetterState copyWith({
    List<CoverLetterModel>? coverLetters,
    CoverLetterModel? currentCoverLetter,
    bool? isLoading,
    bool? isGenerating,
    bool? isSaving,
    String? errorMessage,
    String? successMessage,
  }) {
    return CoverLetterState(
      coverLetters: coverLetters ?? this.coverLetters,
      currentCoverLetter: currentCoverLetter ?? this.currentCoverLetter,
      isLoading: isLoading ?? this.isLoading,
      isGenerating: isGenerating ?? this.isGenerating,
      isSaving: isSaving ?? this.isSaving,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  @override
  List<Object?> get props => [
        coverLetters,
        currentCoverLetter,
        isLoading,
        isGenerating,
        isSaving,
        errorMessage,
        successMessage,
      ];
}
