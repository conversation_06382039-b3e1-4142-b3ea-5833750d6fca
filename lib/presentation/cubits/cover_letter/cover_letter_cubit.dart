import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

import '../../../data/models/cover_letter_model.dart';
import '../../../data/models/simple_resume_model.dart';
import '../../../core/services/open_ai_service.dart';

part 'cover_letter_state.dart';

class CoverLetterCubit extends Cubit<CoverLetterState> {
  final AiGenerator _aiGenerator;
  final Uuid _uuid = const Uuid();

  CoverLetterCubit(this._aiGenerator) : super(const CoverLetterState());

  /// Generate a cover letter based on resume data and job information
  Future<void> generateCoverLetter({
    required ResumeModel resume,
    required String companyName,
    required String positionTitle,
    required String jobDescription,
  }) async {
    emit(state.copyWith(
      isGenerating: true,
      errorMessage: null,
      successMessage: null,
    ));

    try {
      // Convert resume data to a readable format for AI
      final resumeData = _formatResumeForAI(resume);

      // Generate cover letter using AI
      final response = await _aiGenerator.generateCoverLetter(
        resumeData: resumeData,
        jobDescription: jobDescription,
        companyName: companyName,
        positionTitle: positionTitle,
      );

      if (response.isSuccess && response.content != null) {
        final coverLetter = CoverLetterModel(
          id: _uuid.v4(),
          userId: '', // Will be set when saving
          resumeId: resume.id,
          companyName: companyName,
          positionTitle: positionTitle,
          jobDescription: jobDescription,
          content: response.content!,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        emit(state.copyWith(
          currentCoverLetter: coverLetter,
          isGenerating: false,
          successMessage: 'Cover letter generated successfully!',
        ));
      } else {
        emit(state.copyWith(
          isGenerating: false,
          errorMessage: response.error?.message ?? 'Failed to generate cover letter',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isGenerating: false,
        errorMessage: 'Error generating cover letter: ${e.toString()}',
      ));
    }
  }

  /// Format resume data for AI processing
  String _formatResumeForAI(ResumeModel resume) {
    final buffer = StringBuffer();

    // Personal Information
    buffer.writeln('PERSONAL INFORMATION:');
    buffer.writeln('Name: ${resume.personalInfo.firstName} ${resume.personalInfo.lastName}');
    buffer.writeln('Email: ${resume.personalInfo.email}');
    buffer.writeln('Phone: ${resume.personalInfo.phone}');
    if (resume.personalInfo.address.isNotEmpty) {
      buffer.writeln('Address: ${resume.personalInfo.address}');
    }
    buffer.writeln();

    // Summary
    if (resume.summary.isNotEmpty) {
      buffer.writeln('PROFESSIONAL SUMMARY:');
      buffer.writeln(resume.summary);
      buffer.writeln();
    }

    // Work Experience
    if (resume.workExperience.isNotEmpty) {
      buffer.writeln('WORK EXPERIENCE:');
      for (final work in resume.workExperience) {
        buffer.writeln('${work.jobTitle} at ${work.company}');
        buffer.writeln('${work.startDate.year} - ${work.isCurrentJob ? 'Present' : work.endDate?.year}');
        buffer.writeln('Location: ${work.location}');
        if (work.description.isNotEmpty) {
          buffer.writeln('Description: ${work.description}');
        }
        if (work.achievements.isNotEmpty) {
          buffer.writeln('Achievements:');
          for (final achievement in work.achievements) {
            buffer.writeln('• $achievement');
          }
        }
        buffer.writeln();
      }
    }

    // Education
    if (resume.education.isNotEmpty) {
      buffer.writeln('EDUCATION:');
      for (final edu in resume.education) {
        buffer.writeln(edu.degree);
        buffer.writeln('${edu.institution} (${edu.startDate.year} - ${edu.endDate?.year ?? 'Present'})');
        if (edu.gpa != null && edu.gpa!.isNotEmpty) {
          buffer.writeln('GPA: ${edu.gpa}');
        }
        buffer.writeln();
      }
    }

    // Skills
    if (resume.skills.isNotEmpty) {
      buffer.writeln('SKILLS:');
      for (final skillCategory in resume.skills) {
        buffer.writeln('${skillCategory.category}:');
        for (final skill in skillCategory.skills) {
          final levelText = _getProficiencyText(skill.proficiencyLevel);
          buffer.writeln('• ${skill.name} ($levelText)');
        }
        buffer.writeln();
      }
    }

    // Projects
    if (resume.projects.isNotEmpty) {
      buffer.writeln('PROJECTS:');
      for (final project in resume.projects) {
        buffer.writeln(project.name);
        if (project.description.isNotEmpty) {
          buffer.writeln('Description: ${project.description}');
        }
        if (project.technologies.isNotEmpty) {
          buffer.writeln('Technologies: ${project.technologies.join(', ')}');
        }
        buffer.writeln();
      }
    }

    // Certifications
    if (resume.certifications.isNotEmpty) {
      buffer.writeln('CERTIFICATIONS:');
      for (final cert in resume.certifications) {
        buffer.writeln('${cert.name} - ${cert.issuer}');
        buffer.writeln('Issued: ${cert.issueDate.year}');
        buffer.writeln();
      }
    }

    return buffer.toString();
  }

  /// Clear the current cover letter
  void clearCurrentCoverLetter() {
    emit(state.copyWith(
      currentCoverLetter: null,
      errorMessage: null,
      successMessage: null,
    ));
  }

  /// Clear error message
  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  /// Clear success message
  void clearSuccess() {
    emit(state.copyWith(successMessage: null));
  }

  /// Update the current cover letter content
  void updateCoverLetterContent(String content) {
    if (state.currentCoverLetter != null) {
      final updatedCoverLetter = state.currentCoverLetter!.copyWith(
        content: content,
        updatedAt: DateTime.now(),
      );
      emit(state.copyWith(currentCoverLetter: updatedCoverLetter));
    }
  }

  /// Convert proficiency level to text
  String _getProficiencyText(int level) {
    switch (level) {
      case 1:
        return 'Beginner';
      case 2:
        return 'Intermediate';
      case 3:
        return 'Advanced';
      case 4:
        return 'Expert';
      case 5:
        return 'Master';
      default:
        return 'Intermediate';
    }
  }
}
