import 'package:flutter/material.dart';

import '../../../data/models/simple_resume_model.dart';

class CoverLetterFormWidget extends StatefulWidget {
  final ResumeModel resume;
  final bool isGenerating;
  final Function(String companyName, String positionTitle, String jobDescription) onGenerate;

  const CoverLetterFormWidget({
    super.key,
    required this.resume,
    required this.isGenerating,
    required this.onGenerate,
  });

  @override
  State<CoverLetterFormWidget> createState() => _CoverLetterFormWidgetState();
}

class _CoverLetterFormWidgetState extends State<CoverLetterFormWidget> {
  final _formKey = GlobalKey<FormState>();
  final _companyNameController = TextEditingController();
  final _positionTitleController = TextEditingController();
  final _jobDescriptionController = TextEditingController();

  @override
  void dispose() {
    _companyNameController.dispose();
    _positionTitleController.dispose();
    _jobDescriptionController.dispose();
    super.dispose();
  }

  void _generateCoverLetter() {
    if (_formKey.currentState!.validate()) {
      widget.onGenerate(
        _companyNameController.text.trim(),
        _positionTitleController.text.trim(),
        _jobDescriptionController.text.trim(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.auto_awesome,
                    size: 48,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Generate AI Cover Letter',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Create a personalized cover letter using AI based on your resume',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.9),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Resume Info Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.person,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Using Resume',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        TextButton.icon(
                          onPressed: () {
                            Navigator.of(context).pushNamed('/resume-selection');
                          },
                          icon: const Icon(Icons.swap_horiz, size: 18),
                          label: const Text('Change'),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '${widget.resume.personalInfo.firstName} ${widget.resume.personalInfo.lastName}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (widget.resume.personalInfo.email.isNotEmpty)
                      Text(
                        widget.resume.personalInfo.email,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Form Fields
            Text(
              'Job Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Company Name Field
            TextFormField(
              controller: _companyNameController,
              decoration: InputDecoration(
                labelText: 'Company Name *',
                hintText: 'e.g., Google, Microsoft, Apple',
                prefixIcon: const Icon(Icons.business),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter the company name';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Position Title Field
            TextFormField(
              controller: _positionTitleController,
              decoration: InputDecoration(
                labelText: 'Position Title *',
                hintText: 'e.g., Software Engineer, Product Manager',
                prefixIcon: const Icon(Icons.work),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter the position title';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Job Description Field
            TextFormField(
              controller: _jobDescriptionController,
              decoration: InputDecoration(
                labelText: 'Job Description *',
                hintText: 'Paste the job description here...',
                prefixIcon: const Icon(Icons.description),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                alignLabelWithHint: true,
              ),
              maxLines: 8,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter the job description';
                }
                if (value.trim().length < 50) {
                  return 'Job description should be at least 50 characters';
                }
                return null;
              },
            ),

            const SizedBox(height: 32),

            // Generate Button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: FilledButton.icon(
                onPressed: widget.isGenerating ? null : _generateCoverLetter,
                icon: widget.isGenerating
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.auto_awesome),
                label: Text(
                  widget.isGenerating ? 'Generating...' : 'Generate Cover Letter',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: FilledButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Tips Card
            Card(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Tips for Better Results',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text('• Include the complete job description for better matching'),
                    const Text('• Use the exact company name and position title'),
                    const Text('• Make sure your resume is complete and up-to-date'),
                    const Text('• Review and customize the generated cover letter'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
