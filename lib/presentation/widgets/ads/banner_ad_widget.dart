import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import '../../../core/services/ad_service.dart';
import '../../cubits/remote_config/remote_config_cubit.dart';

class BannerAdWidget extends StatefulWidget {
  final EdgeInsets? margin;
  final Color? backgroundColor;

  const BannerAdWidget({
    super.key,
    this.margin,
    this.backgroundColor,
  });

  @override
  State<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;
  bool _isAdFailed = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  void _loadBannerAd() {
    // Check if banner ads are enabled via Remote Config
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    if (!remoteConfigCubit.shouldShowBannerAds()) {
      return; // Don't load ad if disabled
    }

    AdService.instance.loadBannerAd(
      onAdLoaded: (ad) {
        setState(() {
          _bannerAd = ad;
          _isAdLoaded = true;
          _isAdFailed = false;
        });
      },
      onAdFailedToLoad: (error) {
        setState(() {
          _isAdLoaded = false;
          _isAdFailed = true;
        });
      },
    );
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RemoteConfigCubit, RemoteConfigState>(
      listener: (context, state) {
        // Reload ad when Remote Config changes
        if (state is RemoteConfigLoaded) {
          _loadBannerAd();
        }
      },
      child: _buildAdWidget(),
    );
  }

  Widget _buildAdWidget() {
    // Check if banner ads are enabled
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    if (!remoteConfigCubit.shouldShowBannerAds()) {
      return const SizedBox.shrink(); // Hide if disabled
    }

    if (_isAdLoaded && _bannerAd != null) {
      return Container(
        margin: widget.margin ?? const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: SizedBox(
            width: _bannerAd!.size.width.toDouble(),
            height: _bannerAd!.size.height.toDouble(),
            child: AdWidget(ad: _bannerAd!),
          ),
        ),
      );
    } else if (_isAdFailed) {
      // Show placeholder when ad fails to load (optional)
      return Container(
        margin: widget.margin ?? const EdgeInsets.all(8),
        height: 60,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.info_outline, color: Colors.grey.shade600, size: 16),
              const SizedBox(width: 8),
              Text(
                'Ad not available',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Show loading placeholder
      return Container(
        margin: widget.margin ?? const EdgeInsets.all(8),
        height: 60,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Loading ad...',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
}
