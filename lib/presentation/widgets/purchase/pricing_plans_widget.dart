import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/purchase_model.dart';
import '../../cubits/purchase/purchase_cubit.dart';

class PricingPlansWidget extends StatelessWidget {
  final bool showHeader;
  final VoidCallback? onPurchaseSuccess;

  const PricingPlansWidget({
    super.key,
    this.showHeader = true,
    this.onPurchaseSuccess,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PurchaseCubit, PurchaseState>(
      listener: (context, state) {
        if (state is PurchaseLoaded) {
          if (state.successMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.successMessage!),
                backgroundColor: Colors.green,
              ),
            );
            onPurchaseSuccess?.call();
            context.read<PurchaseCubit>().clearMessages();
          }
          
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
            context.read<PurchaseCubit>().clearMessages();
          }
        }
      },
      builder: (context, state) {
        debugPrint('DEBUG: PricingPlansWidget - Current state: ${state.runtimeType}');

        if (state is PurchaseLoading) {
          debugPrint('DEBUG: PricingPlansWidget - Showing loading spinner');
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is PurchaseError) {
          debugPrint('DEBUG: PricingPlansWidget - Showing error: ${state.message}');
          return _buildErrorWidget(context, state.message);
        }

        if (state is PurchaseDisabled) {
          debugPrint('DEBUG: PricingPlansWidget - Purchases disabled: ${state.message}');
          return _buildDisabledWidget(context, state.message, state.detailedMessage);
        }

        if (state is PurchaseLoaded) {
          debugPrint('DEBUG: PricingPlansWidget - Loaded with ${state.products.length} products');
          return _buildPricingPlans(context, state);
        }

        debugPrint('DEBUG: PricingPlansWidget - State is PurchaseInitial, showing empty widget');
        return _buildInitialWidget(context);
      },
    );
  }

  Widget _buildInitialWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading pricing plans...',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'Initializing purchase system',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.read<PurchaseCubit>().initialize(),
            child: const Text('Initialize Purchases'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String error) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Unable to load pricing plans',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.read<PurchaseCubit>().initialize(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildDisabledWidget(BuildContext context, String message, String? detailedMessage) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.block,
            size: 64,
            color: Colors.orange,
          ),
          const SizedBox(height: 16),
          Text(
            'Purchases Temporarily Unavailable',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          if (detailedMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              detailedMessage,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.read<PurchaseCubit>().initialize(),
            child: const Text('Check Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingPlans(BuildContext context, PurchaseLoaded state) {
    if (state.products.isEmpty) {
      debugPrint('DEBUG: PricingPlansWidget - ❌ NO PRODUCTS FOUND!');
      debugPrint('DEBUG: PricingPlansWidget - This means products failed to load from Google Play Console');
      debugPrint('DEBUG: PricingPlansWidget - Check: 1) Products exist in Play Console 2) App is signed with release key 3) Testing from Internal Testing track');

      return Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'No pricing plans available',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Products not found in Google Play Console.\nCheck setup and try again.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.read<PurchaseCubit>().initialize(),
              child: const Text('Retry Loading'),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showHeader) _buildHeader(context, state),
        const SizedBox(height: 24),
        _buildPlanCards(context, state),
        const SizedBox(height: 24),
        _buildRestoreButton(context, state),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, PurchaseLoaded state) {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF667eea),
            const Color(0xFF764ba2),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.workspace_premium,
              color: Colors.white,
              size: 40,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Unlock Premium Features',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 28,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            'Get unlimited exports, premium templates, and exclusive features',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          if (state.hasExportLimitReached) ...[
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.warning_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Export limit reached! Upgrade now to continue creating amazing resumes.',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPlanCards(BuildContext context, PurchaseLoaded state) {
    // Sort products by preference (yearly, monthly, lifetime)
    final sortedProducts = List<PurchaseProductModel>.from(state.products);
    sortedProducts.sort((a, b) {
      const order = ['premium_yearly', 'premium_monthly', 'premium_lifetime'];
      return order.indexOf(a.id).compareTo(order.indexOf(b.id));
    });

    return Column(
      children: sortedProducts.map((product) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildPlanCard(context, product, state),
        );
      }).toList(),
    );
  }

  Widget _buildPlanCard(BuildContext context, PurchaseProductModel product, PurchaseLoaded state) {
    final isPopular = product.id == 'premium_yearly';
    final isLifetime = product.id == 'premium_lifetime';
    final isMonthly = product.id == 'premium_monthly';

    // Modern color scheme
    Color cardColor = Colors.white;
    Color borderColor = Colors.grey.shade200;
    Color accentColor = const Color(0xFF6366f1); // Indigo
    String? badge;
    Color? badgeColor;
    Color? badgeTextColor;
    List<BoxShadow> shadows = [
      BoxShadow(
        color: Colors.grey.withValues(alpha: 0.1),
        blurRadius: 10,
        offset: const Offset(0, 4),
      ),
    ];

    if (isPopular) {
      cardColor = const Color(0xFFFEF3C7); // Warm yellow background
      borderColor = const Color(0xFFF59E0B); // Amber border
      accentColor = const Color(0xFFF59E0B);
      badge = 'MOST POPULAR';
      badgeColor = const Color(0xFFF59E0B);
      badgeTextColor = Colors.white;
      shadows = [
        BoxShadow(
          color: const Color(0xFFF59E0B).withValues(alpha: 0.3),
          blurRadius: 20,
          offset: const Offset(0, 8),
        ),
      ];
    } else if (isLifetime) {
      cardColor = const Color(0xFFECFDF5); // Light green background
      borderColor = const Color(0xFF10B981); // Emerald border
      accentColor = const Color(0xFF10B981);
      badge = 'BEST VALUE';
      badgeColor = const Color(0xFF10B981);
      badgeTextColor = Colors.white;
      shadows = [
        BoxShadow(
          color: const Color(0xFF10B981).withValues(alpha: 0.2),
          blurRadius: 15,
          offset: const Offset(0, 6),
        ),
      ];
    } else if (isMonthly) {
      cardColor = const Color(0xFFF0F9FF); // Light blue background
      borderColor = const Color(0xFF3B82F6); // Blue border
      accentColor = const Color(0xFF3B82F6);
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: cardColor,
        border: Border.all(
          color: borderColor,
          width: isPopular ? 3 : 2,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: shadows,
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(28),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Plan icon and title
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: accentColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        _getPlanIcon(product.id),
                        color: accentColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getPlanTitle(product.id),
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 22,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getPlanSubtitle(product.id),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Price section
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      product.price,
                      style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 36,
                        color: accentColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Text(
                        _getPricePeriod(product.id),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),

                if (_getSavingsText(product.id) != null) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: accentColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      _getSavingsText(product.id)!,
                      style: TextStyle(
                        color: accentColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 24),

                // Features list
                _buildCardFeaturesList(product.id, accentColor),

                const SizedBox(height: 28),

                // Purchase button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: state.isLoading
                        ? null
                        : () => context.read<PurchaseCubit>().purchaseProduct(product.id),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: accentColor,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: state.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'Choose ${_getPlanTitle(product.id)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
          // Badge
          if (badge != null)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: badgeColor,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(24),
                    bottomLeft: Radius.circular(24),
                  ),
                ),
                child: Text(
                  badge,
                  style: TextStyle(
                    color: badgeTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Helper methods for the new design
  IconData _getPlanIcon(String productId) {
    switch (productId) {
      case 'premium_monthly':
        return Icons.calendar_month;
      case 'premium_yearly':
        return Icons.calendar_today;
      case 'premium_lifetime':
        return Icons.all_inclusive;
      default:
        return Icons.star;
    }
  }

  String _getPricePeriod(String productId) {
    switch (productId) {
      case 'premium_monthly':
        return '/month';
      case 'premium_yearly':
        return '/year';
      case 'premium_lifetime':
        return 'one-time';
      default:
        return '';
    }
  }

  String? _getSavingsText(String productId) {
    switch (productId) {
      case 'premium_yearly':
        return 'Save 60% vs monthly';
      case 'premium_lifetime':
        return 'Best long-term value';
      default:
        return null;
    }
  }

  Widget _buildCardFeaturesList(String productId, Color accentColor) {
    List<String> features;

    switch (productId) {
      case 'premium_monthly':
        features = [
          'Unlimited PDF exports',
          'Premium templates',
          'Priority support',
        ];
        break;
      case 'premium_yearly':
        features = [
          'Everything in Monthly',
          'Advanced templates',
          'Priority support',
          'Cloud sync',
        ];
        break;
      case 'premium_lifetime':
        features = [
          'All premium features',
          'Lifetime updates',
          'VIP support',
          'Early access to new features',
        ];
        break;
      default:
        features = ['Premium features'];
    }

    return Column(
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: accentColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  feature,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }



  Widget _buildRestoreButton(BuildContext context, PurchaseLoaded state) {
    return Center(
      child: Container(
        margin: const EdgeInsets.only(top: 16),
        child: TextButton.icon(
          onPressed: state.isLoading
              ? null
              : () => context.read<PurchaseCubit>().restorePurchases(),
          icon: const Icon(
            Icons.restore,
            size: 18,
            color: Color(0xFF6366f1),
          ),
          label: const Text(
            'Restore Previous Purchases',
            style: TextStyle(
              color: Color(0xFF6366f1),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  String _getPlanTitle(String productId) {
    switch (productId) {
      case 'premium_monthly':
        return 'Monthly Plan';
      case 'premium_yearly':
        return 'Yearly Plan';
      case 'premium_lifetime':
        return 'Lifetime Plan';
      default:
        return 'Premium Plan';
    }
  }

  String _getPlanSubtitle(String productId) {
    switch (productId) {
      case 'premium_monthly':
        return 'Perfect for short-term use';
      case 'premium_yearly':
        return 'Save 60% with annual billing';
      case 'premium_lifetime':
        return 'One-time payment, lifetime access';
      default:
        return 'Unlock all premium features';
    }
  }

}
