import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubits/remote_config/remote_config_cubit.dart';
import 'remote_config_builder.dart';

/// Example usage of Remote Config widgets and functionality
class RemoteConfigExamples extends StatelessWidget {
  const RemoteConfigExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Remote Config Examples'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<RemoteConfigCubit>().fetchConfig();
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Example 1: Basic Remote Config Builder
            _buildSection(
              'Basic Configuration',
              RemoteConfigBuilder(
                builder: (context, config) {
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Max Free Resumes: ${config.maxFreeResumes}'),
                          Text('Support Email: ${config.supportEmail}'),
                          Text('Dark Mode Enabled: ${config.enableDarkMode}'),
                          Text('Google Sign-In: ${config.enableGoogleSignIn}'),
                          Text('Apple Sign-In: ${config.enableAppleSignIn}'),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Example 2: Feature Flag Usage
            _buildSection(
              'Feature Flags',
              Column(
                children: [
                  FeatureFlag(
                    featureKey: 'enable_premium_templates',
                    child: const Card(
                      color: Colors.blue,
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text(
                          'Premium Templates Available!',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                    fallback: const Card(
                      color: Colors.grey,
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text('Premium Templates Coming Soon'),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  FeatureFlag(
                    featureKey: 'enable_push_notifications',
                    child: ElevatedButton(
                      onPressed: () {
                        // Enable push notifications
                      },
                      child: const Text('Enable Notifications'),
                    ),
                  ),
                ],
              ),
            ),

            // Example 3: Ad Display Control
            _buildSection(
              'Ad Display',
              Column(
                children: [
                  AdWidget(
                    adType: AdType.banner,
                    child: Container(
                      height: 60,
                      width: double.infinity,
                      color: Colors.yellow,
                      child: const Center(
                        child: Text('Banner Ad Placeholder'),
                      ),
                    ),
                    fallback: const Text('Banner ads disabled'),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      final cubit = context.read<RemoteConfigCubit>();
                      if (cubit.shouldShowInterstitialAds()) {
                        _showInterstitialAdDialog(context);
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Interstitial ads are disabled'),
                          ),
                        );
                      }
                    },
                    child: const Text('Show Interstitial Ad'),
                  ),
                ],
              ),
            ),

            // Example 4: Template Filtering
            _buildSection(
              'Template Filtering',
              TemplateFilter(
                templates: _getSampleTemplates(),
                getTemplateId: (template) => template['id'],
                builder: (visibleTemplates) {
                  return Column(
                    children: [
                      Text('Visible Templates: ${visibleTemplates.length}'),
                      const SizedBox(height: 8),
                      ...visibleTemplates.map((template) => Card(
                        child: ListTile(
                          title: Text(template['name']),
                          subtitle: Text('ID: ${template['id']}'),
                        ),
                      )),
                    ],
                  );
                },
              ),
            ),

            // Example 5: Direct Config Access
            _buildSection(
              'Direct Config Access',
              BlocBuilder<RemoteConfigCubit, RemoteConfigState>(
                builder: (context, state) {
                  final cubit = context.read<RemoteConfigCubit>();
                  
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Ad Frequency: ${cubit.getAdFrequency()}'),
                          Text('Maintenance Mode: ${cubit.isMaintenanceMode()}'),
                          Text('Force Update: ${cubit.isForceUpdateRequired()}'),
                          const SizedBox(height: 8),
                          Text('Custom Value: ${cubit.getConfigValue('custom_key', 'default')}'),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Example 6: App Status Check
            _buildSection(
              'App Status',
              BlocBuilder<RemoteConfigCubit, RemoteConfigState>(
                builder: (context, state) {
                  final cubit = context.read<RemoteConfigCubit>();
                  final appStatus = cubit.getAppStatusConfig();
                  
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                appStatus.isMaintenanceMode 
                                    ? Icons.build 
                                    : Icons.check_circle,
                                color: appStatus.isMaintenanceMode 
                                    ? Colors.orange 
                                    : Colors.green,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                appStatus.isMaintenanceMode 
                                    ? 'Maintenance Mode' 
                                    : 'Service Available',
                              ),
                            ],
                          ),
                          if (appStatus.maintenanceMessage.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text(appStatus.maintenanceMessage),
                          ],
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                appStatus.isForceUpdateRequired 
                                    ? Icons.system_update 
                                    : Icons.check,
                                color: appStatus.isForceUpdateRequired 
                                    ? Colors.red 
                                    : Colors.green,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                appStatus.isForceUpdateRequired 
                                    ? 'Update Required' 
                                    : 'App Up to Date',
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        child,
        const SizedBox(height: 24),
      ],
    );
  }

  List<Map<String, String>> _getSampleTemplates() {
    return [
      {'id': 'template_1', 'name': 'Modern Template'},
      {'id': 'template_2', 'name': 'Classic Template'},
      {'id': 'template_3', 'name': 'Creative Template'},
      {'id': 'template_4', 'name': 'Professional Template'},
      {'id': 'template_5', 'name': 'Minimalist Template'},
    ];
  }

  void _showInterstitialAdDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Interstitial Ad'),
        content: const Text('This would show an interstitial ad.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
