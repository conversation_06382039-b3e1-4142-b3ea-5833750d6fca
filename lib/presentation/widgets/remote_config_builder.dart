import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubits/remote_config/remote_config_cubit.dart';
import '../../data/models/remote_config_model.dart';

/// Widget that builds UI based on Remote Config state
class RemoteConfigBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, RemoteConfigModel config) builder;
  final Widget? loading;
  final Widget? error;

  const RemoteConfigBuilder({
    super.key,
    required this.builder,
    this.loading,
    this.error,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RemoteConfigCubit, RemoteConfigState>(
      builder: (context, state) {
        if (state is RemoteConfigLoaded) {
          return builder(context, state.config);
        } else if (state is RemoteConfigLoading) {
          return loading ?? const Center(child: CircularProgressIndicator());
        } else if (state is RemoteConfigError) {
          return error ?? 
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 48),
                    const SizedBox(height: 16),
                    Text(
                      'Configuration Error',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
        }
        
        // Initial state - show loading
        return loading ?? const Center(child: CircularProgressIndicator());
      },
    );
  }
}

/// Widget that conditionally shows content based on feature flags
class FeatureFlag extends StatelessWidget {
  final String featureKey;
  final Widget child;
  final Widget? fallback;

  const FeatureFlag({
    super.key,
    required this.featureKey,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    final isEnabled = remoteConfigCubit.isFeatureEnabled(featureKey);
    
    if (isEnabled) {
      return child;
    }
    
    return fallback ?? const SizedBox.shrink();
  }
}

/// Widget that conditionally shows ads based on Remote Config
class AdWidget extends StatelessWidget {
  final AdType adType;
  final Widget child;
  final Widget? fallback;

  const AdWidget({
    super.key,
    required this.adType,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    
    bool shouldShow = false;
    switch (adType) {
      case AdType.banner:
        shouldShow = remoteConfigCubit.shouldShowBannerAds();
        break;
      case AdType.interstitial:
        shouldShow = remoteConfigCubit.shouldShowInterstitialAds();
        break;
    }
    
    if (shouldShow) {
      return child;
    }
    
    return fallback ?? const SizedBox.shrink();
  }
}

/// Widget that filters templates based on visibility settings
class TemplateFilter extends StatelessWidget {
  final List<dynamic> templates;
  final String Function(dynamic) getTemplateId;
  final Widget Function(List<dynamic>) builder;

  const TemplateFilter({
    super.key,
    required this.templates,
    required this.getTemplateId,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    
    final visibleTemplates = templates.where((template) {
      final templateId = getTemplateId(template);
      return remoteConfigCubit.isTemplateVisible(templateId);
    }).toList();
    
    return builder(visibleTemplates);
  }
}

/// Widget that shows maintenance mode screen
class MaintenanceCheck extends StatelessWidget {
  final Widget child;

  const MaintenanceCheck({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    
    if (remoteConfigCubit.isMaintenanceMode()) {
      final appStatus = remoteConfigCubit.getAppStatusConfig();
      
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.build,
                  size: 80,
                  color: Colors.orange,
                ),
                const SizedBox(height: 24),
                Text(
                  'Under Maintenance',
                  style: Theme.of(context).textTheme.headlineMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  appStatus.maintenanceMessage.isNotEmpty
                      ? appStatus.maintenanceMessage
                      : 'We are currently performing maintenance. Please try again later.',
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () {
                    remoteConfigCubit.fetchConfig();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      );
    }
    
    return child;
  }
}

/// Widget that shows force update dialog
class ForceUpdateCheck extends StatelessWidget {
  final Widget child;

  const ForceUpdateCheck({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final remoteConfigCubit = context.read<RemoteConfigCubit>();
    
    if (remoteConfigCubit.isForceUpdateRequired()) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showForceUpdateDialog(context, remoteConfigCubit);
      });
    }
    
    return child;
  }

  void _showForceUpdateDialog(BuildContext context, RemoteConfigCubit cubit) {
    final appStatus = cubit.getAppStatusConfig();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Update Required'),
        content: Text(
          appStatus.updateMessage.isNotEmpty
              ? appStatus.updateMessage
              : 'A new version of the app is available. Please update to continue.',
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              // TODO: Implement app store navigation
              // You can use url_launcher to open app store
            },
            child: const Text('Update Now'),
          ),
        ],
      ),
    );
  }
}

enum AdType {
  banner,
  interstitial,
}
