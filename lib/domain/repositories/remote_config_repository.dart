import '../../data/models/remote_config_model.dart';

abstract class RemoteConfigRepository {
  /// Initialize Remote Config
  Future<void> initialize();

  /// Fetch latest config from server
  Future<bool> fetchConfig();

  /// Get current config
  RemoteConfigModel getConfig();

  /// Get specific boolean value
  bool getBool(String key);

  /// Get specific string value
  String getString(String key);

  /// Get specific int value
  int getInt(String key);

  /// Check if template should be visible
  bool isTemplateVisible(String templateId);

  /// Check if ads should be shown
  bool shouldShowBannerAds();
  bool shouldShowInterstitialAds();

  /// Check if feature is enabled
  bool isFeatureEnabled(String featureKey);

  /// Get ad frequency
  int getAdFrequency();

  /// Check if app is in maintenance mode
  bool isMaintenanceMode();

  /// Check if force update is required
  bool isForceUpdateRequired();

  /// Check if in-app purchases are enabled
  bool areInAppPurchasesEnabled();

  /// Listen to config updates
  Stream<void> get onConfigUpdated;
}
