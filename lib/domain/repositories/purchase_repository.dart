import '../../data/models/purchase_model.dart';

abstract class PurchaseRepository {
  /// Initialize the purchase system
  Future<bool> initialize();
  
  /// Get available products for purchase
  Future<List<PurchaseProductModel>> getAvailableProducts();
  
  /// Purchase a product
  Future<bool> purchaseProduct(String productId);
  
  /// Restore previous purchases
  Future<void> restorePurchases();
  
  /// Save a completed purchase
  Future<void> savePurchase(PurchaseModel purchase);
  
  /// Get user's purchase history
  Future<List<PurchaseModel>> getUserPurchases(String userId);
  
  /// Get user's subscription status
  Future<UserSubscriptionModel?> getUserSubscription(String userId);
  
  /// Check if user has active premium subscription
  Future<bool> hasActivePremiumSubscription(String userId);
  
  /// Update user subscription status
  Future<void> updateUserSubscription({
    required String userId,
    required SubscriptionType subscriptionType,
    required bool isActive,
    String? productId,
    DateTime? subscriptionEnd,
  });
  
  /// Process a successful purchase
  Future<void> processPurchase(PurchaseModel purchase);
  
  /// Cancel user subscription
  Future<void> cancelSubscription(String userId);
  
  /// Get current authenticated user's subscription
  Future<UserSubscriptionModel?> getCurrentUserSubscription();
  
  /// Stream of purchase events
  Stream<PurchaseModel> get purchaseStream;
  
  /// Stream of available products
  Stream<List<PurchaseProductModel>> get productsStream;
  
  /// Stream of purchase errors
  Stream<String> get errorStream;
}
