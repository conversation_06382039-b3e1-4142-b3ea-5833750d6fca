import '../../data/models/user_export_count_model.dart';

abstract class UserExportRepository {
  /// Get user export count data
  Future<UserExportCountModel?> getUserExportCount(String userId);
  
  /// Get current authenticated user's export count
  Future<UserExportCountModel?> getCurrentUserExportCount();
  
  /// Save or update user export count
  Future<void> saveUserExportCount(UserExportCountModel exportCount);
  
  /// Increment user export count
  Future<UserExportCountModel> incrementExportCount(String userId);
  
  /// Reset user export count
  Future<UserExportCountModel> resetExportCount(String userId);
  
  /// Update user premium status
  Future<void> updatePremiumStatus(String userId, bool isPremium);
  
  /// Check if user needs export count reset and reset if needed
  Future<bool> checkAndResetIfNeeded(String userId);
  
  /// Delete user export count data
  Future<void> deleteUserExportCount(String userId);
  
  /// Check if user can export (within limits)
  Future<bool> canUserExport(String userId, int maxFreeExports, int maxPremiumExports);
  
  /// Validate export attempt and throw exception if limit exceeded
  Future<void> validateExportAttempt(String userId, int maxFreeExports, int maxPremiumExports);
}
