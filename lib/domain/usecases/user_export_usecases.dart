import 'package:flutter/material.dart';

import '../repositories/user_export_repository.dart';
import '../../data/models/user_export_count_model.dart';

class UserExportUseCases {
  final UserExportRepository _repository;

  UserExportUseCases(this._repository);

  /// Get user export count data
  Future<UserExportCountModel?> getUserExportCount(String userId) async {
    return await _repository.getUserExportCount(userId);
  }

  /// Get current authenticated user's export count
  Future<UserExportCountModel?> getCurrentUserExportCount() async {
    return await _repository.getCurrentUserExportCount();
  }

  /// Save or update user export count
  Future<void> saveUserExportCount(UserExportCountModel exportCount) async {
    return await _repository.saveUserExportCount(exportCount);
  }

  /// Increment user export count after successful export
  Future<UserExportCountModel> incrementExportCount(String userId) async {
    debugPrint('DEBUG: UseCase - Incrementing export count for user: $userId');
    return await _repository.incrementExportCount(userId);
  }

  /// Reset user export count (admin function)
  Future<UserExportCountModel> resetExportCount(String userId) async {
    debugPrint('DEBUG: UseCase - Resetting export count for user: $userId');
    return await _repository.resetExportCount(userId);
  }

  /// Update user premium status
  Future<void> updatePremiumStatus(String userId, bool isPremium) async {
    debugPrint('DEBUG: UseCase - Updating premium status for user: $userId to $isPremium');
    return await _repository.updatePremiumStatus(userId, isPremium);
  }

  /// Check if user needs export count reset and reset if needed
  Future<bool> checkAndResetIfNeeded(String userId) async {
    return await _repository.checkAndResetIfNeeded(userId);
  }

  /// Delete user export count data
  Future<void> deleteUserExportCount(String userId) async {
    return await _repository.deleteUserExportCount(userId);
  }

  /// Check if user can export based on limits
  Future<bool> canUserExport(String userId, int maxFreeExports, int maxPremiumExports) async {
    debugPrint('DEBUG: UseCase - Checking if user $userId can export');
    return await _repository.canUserExport(userId, maxFreeExports, maxPremiumExports);
  }

  /// Validate export attempt and throw exception if limit exceeded
  Future<void> validateExportAttempt(String userId, int maxFreeExports, int maxPremiumExports) async {
    debugPrint('DEBUG: UseCase - Validating export attempt for user: $userId');
    return await _repository.validateExportAttempt(userId, maxFreeExports, maxPremiumExports);
  }

  /// Complete export process: validate, export, and increment count
  Future<void> processExportAttempt(String userId, int maxFreeExports, int maxPremiumExports) async {
    debugPrint('DEBUG: UseCase - Processing export attempt for user: $userId');
    
    // First validate the export attempt
    await validateExportAttempt(userId, maxFreeExports, maxPremiumExports);
    
    // If validation passes, increment the count
    await incrementExportCount(userId);
    
    debugPrint('DEBUG: UseCase - Export attempt processed successfully');
  }

  /// Get export status information for UI display
  Future<ExportStatusInfo> getExportStatusInfo(String userId, int maxFreeExports, int maxPremiumExports) async {
    try {
      await checkAndResetIfNeeded(userId);
      
      final exportData = await getUserExportCount(userId);
      if (exportData == null) {
        return ExportStatusInfo(
          canExport: true,
          currentCount: 0,
          maxAllowed: maxFreeExports,
          isPremiumUser: false,
          remainingExports: maxFreeExports,
        );
      }

      final maxAllowed = exportData.isPremiumUser ? maxPremiumExports : maxFreeExports;
      final canExport = exportData.isPremiumUser && maxPremiumExports == -1 
          ? true 
          : exportData.exportCount < maxAllowed;
      
      final remainingExports = exportData.isPremiumUser && maxPremiumExports == -1
          ? -1 // Unlimited
          : maxAllowed - exportData.exportCount;

      return ExportStatusInfo(
        canExport: canExport,
        currentCount: exportData.exportCount,
        maxAllowed: maxAllowed,
        isPremiumUser: exportData.isPremiumUser,
        remainingExports: remainingExports,
        lastExportDate: exportData.lastExportDate,
        resetDate: exportData.resetDate,
      );
    } catch (e) {
      debugPrint('DEBUG: Error getting export status: $e');
      // Return safe defaults in case of error
      return ExportStatusInfo(
        canExport: true,
        currentCount: 0,
        maxAllowed: maxFreeExports,
        isPremiumUser: false,
        remainingExports: maxFreeExports,
      );
    }
  }
}

/// Information about user's export status for UI display
class ExportStatusInfo {
  final bool canExport;
  final int currentCount;
  final int maxAllowed;
  final bool isPremiumUser;
  final int remainingExports; // -1 means unlimited
  final DateTime? lastExportDate;
  final DateTime? resetDate;

  const ExportStatusInfo({
    required this.canExport,
    required this.currentCount,
    required this.maxAllowed,
    required this.isPremiumUser,
    required this.remainingExports,
    this.lastExportDate,
    this.resetDate,
  });

  String get statusMessage {
    if (isPremiumUser && remainingExports == -1) {
      return 'Premium: Unlimited exports';
    }
    
    if (canExport) {
      return 'Exports remaining: $remainingExports of $maxAllowed';
    } else {
      return isPremiumUser 
          ? 'Premium export limit reached ($maxAllowed)'
          : 'Free export limit reached ($maxAllowed). Upgrade for unlimited exports.';
    }
  }
}
