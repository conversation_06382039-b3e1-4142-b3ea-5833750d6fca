import '../repositories/resume_repository.dart';
import '../../data/models/simple_resume_model.dart';
import '../../data/models/resume_template_model.dart';

class ResumeUseCases {
  final ResumeRepository _resumeRepository;

  ResumeUseCases(this._resumeRepository);

  Future<ResumeModel> getResume(String resumeId) async {
    return await _resumeRepository.getResume(resumeId);
  }

  Future<List<ResumeModel>> getUserResumes(String userId) async {
    return await _resumeRepository.getUserResumes(userId);
  }

  Future<void> saveResume(ResumeModel resume) async {
    return await _resumeRepository.saveResume(resume);
  }

  Future<void> deleteResume(String resumeId) async {
    return await _resumeRepository.deleteResume(resumeId);
  }

  Future<ResumeModel> duplicateResume(String resumeId) async {
    return await _resumeRepository.duplicateResume(resumeId);
  }

  Future<void> exportToPdf(ResumeModel resume, {ResumeTemplateModel? template}) async {
    return await _resumeRepository.exportToPdf(resume, template: template);
  }

  Future<void> exportToPdfWithLimitCheck(ResumeModel resume, {ResumeTemplateModel? template}) async {
    return await _resumeRepository.exportToPdfWithLimitCheck(resume, template: template);
  }

  Future<String> shareResume(String resumeId) async {
    return await _resumeRepository.shareResume(resumeId);
  }

  Future<void> saveResumeLocally(ResumeModel resume) async {
    return await _resumeRepository.saveResumeLocally(resume);
  }

  Future<ResumeModel?> getLocalResume() async {
    return await _resumeRepository.getLocalResume();
  }
}
