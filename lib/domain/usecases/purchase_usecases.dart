import 'package:flutter/material.dart';

import '../repositories/purchase_repository.dart';
import '../repositories/user_export_repository.dart';
import '../../data/models/purchase_model.dart';

class PurchaseUseCases {
  final PurchaseRepository _purchaseRepository;
  final UserExportRepository _userExportRepository;

  PurchaseUseCases(
    this._purchaseRepository,
    this._userExportRepository,
  );

  /// Initialize the purchase system
  Future<bool> initialize() async {
    debugPrint('DEBUG: Purchase UseCases - Initializing purchase system');
    return await _purchaseRepository.initialize();
  }

  /// Get available products for purchase
  Future<List<PurchaseProductModel>> getAvailableProducts() async {
    debugPrint('DEBUG: Purchase UseCases - Getting available products');
    return await _purchaseRepository.getAvailableProducts();
  }

  /// Purchase a product
  Future<bool> purchaseProduct(String productId) async {
    debugPrint('DEBUG: Purchase UseCases - Purchasing product: $productId');
    return await _purchaseRepository.purchaseProduct(productId);
  }

  /// Restore previous purchases
  Future<void> restorePurchases() async {
    debugPrint('DEBUG: Purchase UseCases - Restoring purchases');
    return await _purchaseRepository.restorePurchases();
  }

  /// Get user's purchase history
  Future<List<PurchaseModel>> getUserPurchases(String userId) async {
    return await _purchaseRepository.getUserPurchases(userId);
  }

  /// Get user's subscription status
  Future<UserSubscriptionModel?> getUserSubscription(String userId) async {
    return await _purchaseRepository.getUserSubscription(userId);
  }

  /// Check if user has active premium subscription
  Future<bool> hasActivePremiumSubscription(String userId) async {
    debugPrint('DEBUG: Purchase UseCases - Checking premium status for user: $userId');
    return await _purchaseRepository.hasActivePremiumSubscription(userId);
  }

  /// Get current authenticated user's subscription
  Future<UserSubscriptionModel?> getCurrentUserSubscription() async {
    return await _purchaseRepository.getCurrentUserSubscription();
  }

  /// Cancel user subscription
  Future<void> cancelSubscription(String userId) async {
    debugPrint('DEBUG: Purchase UseCases - Canceling subscription for user: $userId');
    return await _purchaseRepository.cancelSubscription(userId);
  }

  /// Process a successful purchase and update export limits
  Future<void> processPurchaseAndUpdateExportLimits(PurchaseModel purchase) async {
    try {
      debugPrint('DEBUG: Purchase UseCases - Processing purchase and updating export limits');
      
      // Process the purchase
      await _purchaseRepository.processPurchase(purchase);
      
      // Update user's premium status in export tracking
      await _userExportRepository.updatePremiumStatus(purchase.userId, true);
      
      debugPrint('DEBUG: Purchase UseCases - Purchase processed and export limits updated');
    } catch (e) {
      debugPrint('DEBUG: Purchase UseCases - Error processing purchase: $e');
      rethrow;
    }
  }

  /// Update user's premium status in export system
  Future<void> updateExportPremiumStatus(String userId, bool isPremium) async {
    debugPrint('DEBUG: Purchase UseCases - Updating export premium status: $userId -> $isPremium');
    await _userExportRepository.updatePremiumStatus(userId, isPremium);
  }

  /// Get premium status for export limits
  Future<bool> getPremiumStatusForExports(String userId) async {
    try {
      // First check subscription status
      final hasActiveSub = await hasActivePremiumSubscription(userId);
      
      // Update export system with current status
      await updateExportPremiumStatus(userId, hasActiveSub);
      
      return hasActiveSub;
    } catch (e) {
      debugPrint('DEBUG: Purchase UseCases - Error getting premium status: $e');
      return false;
    }
  }

  /// Sync subscription status with export system
  Future<void> syncSubscriptionWithExportSystem(String userId) async {
    try {
      debugPrint('DEBUG: Purchase UseCases - Syncing subscription with export system');
      
      final isPremium = await hasActivePremiumSubscription(userId);
      await updateExportPremiumStatus(userId, isPremium);
      
      debugPrint('DEBUG: Purchase UseCases - Sync completed, premium status: $isPremium');
    } catch (e) {
      debugPrint('DEBUG: Purchase UseCases - Error syncing subscription: $e');
    }
  }

  /// Get purchase status info for UI
  Future<PurchaseStatusInfo> getPurchaseStatusInfo(String userId) async {
    try {
      final subscription = await getUserSubscription(userId);
      final isActive = await hasActivePremiumSubscription(userId);
      
      return PurchaseStatusInfo(
        hasActiveSubscription: isActive,
        subscriptionType: subscription?.subscriptionType ?? SubscriptionType.none,
        subscriptionEnd: subscription?.subscriptionEnd,
        productId: subscription?.productId,
      );
    } catch (e) {
      debugPrint('DEBUG: Purchase UseCases - Error getting purchase status: $e');
      return const PurchaseStatusInfo(
        hasActiveSubscription: false,
        subscriptionType: SubscriptionType.none,
      );
    }
  }

  /// Stream getters
  Stream<PurchaseModel> get purchaseStream => _purchaseRepository.purchaseStream;
  Stream<List<PurchaseProductModel>> get productsStream => _purchaseRepository.productsStream;
  Stream<String> get errorStream => _purchaseRepository.errorStream;
}

/// Information about user's purchase status for UI display
class PurchaseStatusInfo {
  final bool hasActiveSubscription;
  final SubscriptionType subscriptionType;
  final DateTime? subscriptionEnd;
  final String? productId;

  const PurchaseStatusInfo({
    required this.hasActiveSubscription,
    required this.subscriptionType,
    this.subscriptionEnd,
    this.productId,
  });

  String get statusMessage {
    if (!hasActiveSubscription) {
      return 'No active subscription';
    }

    switch (subscriptionType) {
      case SubscriptionType.premium:
        if (subscriptionEnd == null) {
          return 'Premium (Lifetime)';
        } else {
          final daysLeft = subscriptionEnd!.difference(DateTime.now()).inDays;
          if (daysLeft > 0) {
            return 'Premium ($daysLeft days left)';
          } else {
            return 'Premium (Expired)';
          }
        }
      case SubscriptionType.premiumPlus:
        return 'Premium Plus';
      case SubscriptionType.none:
        return 'Free';
    }
  }

  bool get isExpiringSoon {
    if (subscriptionEnd == null) return false;
    final daysLeft = subscriptionEnd!.difference(DateTime.now()).inDays;
    return daysLeft <= 7 && daysLeft > 0;
  }
}
