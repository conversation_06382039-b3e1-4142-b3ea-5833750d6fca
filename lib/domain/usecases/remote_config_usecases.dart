import '../repositories/remote_config_repository.dart';
import '../../data/models/remote_config_model.dart';

class InitializeRemoteConfigUseCase {
  final RemoteConfigRepository _repository;

  InitializeRemoteConfigUseCase(this._repository);

  Future<void> call() async {
    await _repository.initialize();
  }
}

class FetchRemoteConfigUseCase {
  final RemoteConfigRepository _repository;

  FetchRemoteConfigUseCase(this._repository);

  Future<bool> call() async {
    return await _repository.fetchConfig();
  }
}

class GetRemoteConfigUseCase {
  final RemoteConfigRepository _repository;

  GetRemoteConfigUseCase(this._repository);

  RemoteConfigModel call() {
    return _repository.getConfig();
  }
}

class CheckTemplateVisibilityUseCase {
  final RemoteConfigRepository _repository;

  CheckTemplateVisibilityUseCase(this._repository);

  bool call(String templateId) {
    return _repository.isTemplateVisible(templateId);
  }
}

class CheckAdDisplayUseCase {
  final RemoteConfigRepository _repository;

  CheckAdDisplayUseCase(this._repository);

  AdDisplayConfig call() {
    return AdDisplayConfig(
      showBannerAds: _repository.shouldShowBannerAds(),
      showInterstitialAds: _repository.shouldShowInterstitialAds(),
      adFrequency: _repository.getAdFrequency(),
    );
  }
}

class CheckFeatureEnabledUseCase {
  final RemoteConfigRepository _repository;

  CheckFeatureEnabledUseCase(this._repository);

  bool call(String featureKey) {
    return _repository.isFeatureEnabled(featureKey);
  }
}

class CheckAppStatusUseCase {
  final RemoteConfigRepository _repository;

  CheckAppStatusUseCase(this._repository);

  AppStatusConfig call() {
    return AppStatusConfig(
      isMaintenanceMode: _repository.isMaintenanceMode(),
      isForceUpdateRequired: _repository.isForceUpdateRequired(),
      maintenanceMessage: _repository.getString('maintenance_message'),
      updateMessage: _repository.getString('update_message'),
    );
  }
}

class GetConfigValueUseCase {
  final RemoteConfigRepository _repository;

  GetConfigValueUseCase(this._repository);

  T call<T>(String key, T defaultValue) {
    try {
      if (T == bool) {
        return _repository.getBool(key) as T;
      } else if (T == String) {
        return _repository.getString(key) as T;
      } else if (T == int) {
        return _repository.getInt(key) as T;
      }
      return defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }
}

// Helper classes for structured data
class AdDisplayConfig {
  final bool showBannerAds;
  final bool showInterstitialAds;
  final int adFrequency;

  AdDisplayConfig({
    required this.showBannerAds,
    required this.showInterstitialAds,
    required this.adFrequency,
  });
}

class AppStatusConfig {
  final bool isMaintenanceMode;
  final bool isForceUpdateRequired;
  final String maintenanceMessage;
  final String updateMessage;

  AppStatusConfig({
    required this.isMaintenanceMode,
    required this.isForceUpdateRequired,
    required this.maintenanceMessage,
    required this.updateMessage,
  });
}
