import 'package:flutter_test/flutter_test.dart';

import 'package:resume_builder/data/models/user_export_count_model.dart';
import 'package:resume_builder/data/models/remote_config_model.dart';

void main() {
  group('User Export Count Tests', () {
    group('UserExportCountModel', () {
      test('should create model with correct properties', () {
        final model = UserExportCountModel(
          userId: 'test-user-id',
          exportCount: 2,
          lastExportDate: DateTime(2024, 1, 15),
          isPremiumUser: false,
        );

        expect(model.userId, 'test-user-id');
        expect(model.exportCount, 2);
        expect(model.isPremiumUser, false);
      });

      test('should increment export count correctly', () {
        final model = UserExportCountModel(
          userId: 'test-user-id',
          exportCount: 2,
          lastExportDate: DateTime(2024, 1, 15),
          isPremiumUser: false,
        );

        final incremented = model.incrementExportCount();

        expect(incremented.exportCount, 3);
        expect(incremented.userId, 'test-user-id');
        expect(incremented.isPremiumUser, false);
      });

      test('should reset export count correctly', () {
        final model = UserExportCountModel(
          userId: 'test-user-id',
          exportCount: 5,
          lastExportDate: DateTime(2024, 1, 15),
          isPremiumUser: false,
        );

        final reset = model.resetExportCount();

        expect(reset.exportCount, 0);
        expect(reset.userId, 'test-user-id');
        expect(reset.resetDate, isNotNull);
      });

      test('should convert to/from JSON correctly', () {
        final model = UserExportCountModel(
          userId: 'test-user-id',
          exportCount: 3,
          lastExportDate: DateTime(2024, 1, 15),
          resetDate: DateTime(2024, 1, 1),
          isPremiumUser: true,
        );

        final json = model.toJson();
        final fromJson = UserExportCountModel.fromJson(json);

        expect(fromJson.userId, model.userId);
        expect(fromJson.exportCount, model.exportCount);
        expect(fromJson.isPremiumUser, model.isPremiumUser);
        expect(fromJson.lastExportDate, model.lastExportDate);
        expect(fromJson.resetDate, model.resetDate);
      });
    });

    group('ExportLimitExceededException', () {
      test('should create exception with correct properties', () {
        const exception = ExportLimitExceededException(
          message: 'Export limit exceeded',
          currentCount: 2,
          maxAllowed: 1,
          isPremiumUser: false,
        );

        expect(exception.message, 'Export limit exceeded');
        expect(exception.currentCount, 2);
        expect(exception.maxAllowed, 1);
        expect(exception.isPremiumUser, false);
        expect(exception.toString(), 'Export limit exceeded');
      });
    });

    group('RemoteConfigModel Export Settings', () {
      test('should have correct default export limits', () {
        const config = RemoteConfigModel();

        expect(config.maxFreeExports, 1);
        expect(config.maxPremiumExports, -1); // Unlimited
        expect(config.enableExportLimits, true);
      });

      test('should allow custom export limits', () {
        const config = RemoteConfigModel(
          maxFreeExports: 3,
          maxPremiumExports: 10,
          enableExportLimits: false,
        );

        expect(config.maxFreeExports, 3);
        expect(config.maxPremiumExports, 10);
        expect(config.enableExportLimits, false);
      });
    });
  });
}
