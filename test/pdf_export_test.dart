import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';

import '../lib/data/models/resume_template_model.dart';

void main() {
  group('PDF Export Template Styling Tests', () {
    setUpAll(() {
      // Initialize any required setup here
    });

    test('PDF export method accepts template parameter', () {
      // Create a test template with distinctive styling
      const testTemplate = ResumeTemplateModel(
        id: 'test_template',
        name: 'Test Template',
        description: 'A template for testing',
        previewIcon: Icons.description,
        category: TemplateCategory.professional,
        isPremium: false,
        style: TemplateStyle(
          primaryColor: Color(0xFF2196F3), // Blue
          secondaryColor: Color(0xFF4CAF50), // Green
          accentColor: Color(0xFFFF9800), // Orange
          fontFamily: 'Roboto',
          headerFontSize: 28.0,
          bodyFontSize: 16.0,
          sectionSpacing: 24.0,
          showProfileImage: true,
          showSectionIcons: true,
          layout: TemplateLayout.singleColumn,
        ),
      );

      // This test verifies that the PDF export method signature accepts a template parameter
      // We're testing the method signature and template structure, not the full export process
      expect(testTemplate.style.primaryColor, equals(const Color(0xFF2196F3)));
      expect(testTemplate.style.headerFontSize, equals(28.0));
      expect(testTemplate.style.sectionSpacing, equals(24.0));
    });

    test('Template styling properties should be accessible', () {
      // Test that all template styling properties are properly defined
      const testTemplate = ResumeTemplateModel(
        id: 'style_test',
        name: 'Style Test Template',
        description: 'Testing style properties',
        previewIcon: Icons.palette,
        category: TemplateCategory.creative,
        isPremium: false,
        style: TemplateStyle(
          primaryColor: Color(0xFFE91E63), // Pink
          secondaryColor: Color(0xFF9C27B0), // Purple
          accentColor: Color(0xFF673AB7), // Deep Purple
          fontFamily: 'Arial',
          headerFontSize: 32.0,
          bodyFontSize: 18.0,
          sectionSpacing: 30.0,
          showProfileImage: false,
          showSectionIcons: false,
          layout: TemplateLayout.twoColumn,
        ),
      );

      // Verify all style properties are accessible
      expect(testTemplate.style.primaryColor, equals(const Color(0xFFE91E63)));
      expect(testTemplate.style.secondaryColor, equals(const Color(0xFF9C27B0)));
      expect(testTemplate.style.accentColor, equals(const Color(0xFF673AB7)));
      expect(testTemplate.style.fontFamily, equals('Arial'));
      expect(testTemplate.style.headerFontSize, equals(32.0));
      expect(testTemplate.style.bodyFontSize, equals(18.0));
      expect(testTemplate.style.sectionSpacing, equals(30.0));
      expect(testTemplate.style.showProfileImage, equals(false));
      expect(testTemplate.style.showSectionIcons, equals(false));
      expect(testTemplate.style.layout, equals(TemplateLayout.twoColumn));
    });

    test('Different templates should have different styling', () {
      // Get two different templates from the repository
      final template1 = TemplateRepository.getTemplateById('classic_professional');
      final template2 = TemplateRepository.getTemplateById('minimal_black');

      // Verify they have different styling properties
      expect(template1.style.primaryColor, isNot(equals(template2.style.primaryColor)));
      expect(template1.style.headerFontSize, isNot(equals(template2.style.headerFontSize)));
      expect(template1.name, isNot(equals(template2.name)));
    });
  });
}
