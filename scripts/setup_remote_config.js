// <PERSON>ript to set up Remote Config parameters in Firebase
// Run this script with: node scripts/setup_remote_config.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK');
  console.error('   Make sure serviceAccountKey.json exists and is valid');
  process.exit(1);
}

// Remote Config parameters to set up
const remoteConfigParameters = {
  // Template Control
  'visible_template_ids': {
    defaultValue: '["classic_professional", "modern_gradient", "minimal_black"]',
    description: 'JSON array of visible template IDs. Empty array means all templates are visible.',
    valueType: 'STRING'
  },
  'enable_premium_templates': {
    defaultValue: true,
    description: 'Enable premium template features',
    valueType: 'BOOLEAN'
  },

  // Ad Control
  'show_banner_ads': {
    defaultValue: true,
    description: 'Show banner advertisements',
    valueType: 'BOOLEAN'
  },
  'show_interstitial_ads': {
    defaultValue: true,
    description: 'Show interstitial advertisements',
    valueType: 'BOOLEAN'
  },
  'ad_frequency': {
    defaultValue: 3,
    description: 'Show ads every N actions',
    valueType: 'NUMBER'
  },

  // App Settings
  'max_free_resumes': {
    defaultValue: 3,
    description: 'Maximum number of free resumes per user',
    valueType: 'NUMBER'
  },
  'enable_dark_mode': {
    defaultValue: true,
    description: 'Enable dark mode option',
    valueType: 'BOOLEAN'
  },
  'enable_google_sign_in': {
    defaultValue: true,
    description: 'Enable Google Sign-In',
    valueType: 'BOOLEAN'
  },
  'enable_apple_sign_in': {
    defaultValue: true,
    description: 'Enable Apple Sign-In',
    valueType: 'BOOLEAN'
  },

  // Contact & Legal
  'support_email': {
    defaultValue: '<EMAIL>',
    description: 'Support contact email',
    valueType: 'STRING'
  },
  'privacy_policy_url': {
    defaultValue: '',
    description: 'Privacy policy URL',
    valueType: 'STRING'
  },
  'terms_of_service_url': {
    defaultValue: '',
    description: 'Terms of service URL',
    valueType: 'STRING'
  },

  // Analytics & Tracking
  'enable_analytics': {
    defaultValue: true,
    description: 'Enable analytics tracking',
    valueType: 'BOOLEAN'
  },
  'enable_crashlytics': {
    defaultValue: true,
    description: 'Enable crash reporting',
    valueType: 'BOOLEAN'
  },
  'enable_push_notifications': {
    defaultValue: true,
    description: 'Enable push notifications',
    valueType: 'BOOLEAN'
  },

  // App Control
  'app_version': {
    defaultValue: '1.0.0',
    description: 'Current app version',
    valueType: 'STRING'
  },
  'force_update': {
    defaultValue: false,
    description: 'Force users to update the app',
    valueType: 'BOOLEAN'
  },
  'update_message': {
    defaultValue: 'A new version is available. Please update to continue.',
    description: 'Message shown for force update',
    valueType: 'STRING'
  },
  'maintenance_mode': {
    defaultValue: false,
    description: 'Put app in maintenance mode',
    valueType: 'BOOLEAN'
  },
  'maintenance_message': {
    defaultValue: 'We are currently performing maintenance. Please try again later.',
    description: 'Message shown during maintenance',
    valueType: 'STRING'
  }
};

async function setupRemoteConfig() {
  console.log('\n🔧 Setting up Firebase Remote Config parameters...\n');

  try {
    // Note: Firebase Admin SDK doesn't support Remote Config management
    // This script provides the configuration that should be manually added
    
    console.log('📋 Remote Config Parameters to Add:');
    console.log('=====================================\n');

    Object.entries(remoteConfigParameters).forEach(([key, config]) => {
      console.log(`Parameter: ${key}`);
      console.log(`  Default Value: ${JSON.stringify(config.defaultValue)}`);
      console.log(`  Type: ${config.valueType}`);
      console.log(`  Description: ${config.description}`);
      console.log('');
    });

    console.log('🚀 Next Steps:');
    console.log('==============');
    console.log('1. Go to Firebase Console: https://console.firebase.google.com/project/resume-d24cb/config');
    console.log('2. Navigate to Remote Config');
    console.log('3. Add each parameter listed above');
    console.log('4. Set the default values as shown');
    console.log('5. Publish the configuration');
    console.log('');

    console.log('💡 Pro Tips:');
    console.log('============');
    console.log('• Use conditions to target specific user groups');
    console.log('• Test changes with a small percentage of users first');
    console.log('• Monitor app performance after configuration changes');
    console.log('• Use A/B testing for feature rollouts');
    console.log('');

    console.log('🎯 Template Visibility Examples:');
    console.log('================================');
    console.log('Show all templates: []');
    console.log('Show only free templates: ["classic_professional", "modern_gradient", "minimal_black"]');
    console.log('Show premium only: ["executive_blue", "tech_stack", "creative_portfolio", "minimal_green"]');
    console.log('Hide specific template: Remove ID from the array');
    console.log('');

    console.log('✅ Configuration guide generated successfully!');
    console.log('   Copy the parameters above to Firebase Console Remote Config');

  } catch (error) {
    console.error('❌ Error generating configuration:', error);
  }
}

// Generate sample conditions for different user segments
function generateSampleConditions() {
  console.log('\n🎯 Sample Conditions for User Targeting:');
  console.log('========================================\n');

  const conditions = [
    {
      name: 'premium_users',
      expression: 'user.userProperty["subscription"] == "premium"',
      description: 'Target premium subscribers'
    },
    {
      name: 'new_users',
      expression: 'user.firstOpenTime > timestamp("2024-01-01T00:00:00Z")',
      description: 'Target users who first opened the app after Jan 1, 2024'
    },
    {
      name: 'ios_users',
      expression: 'device.os == "ios"',
      description: 'Target iOS users'
    },
    {
      name: 'android_users',
      expression: 'device.os == "android"',
      description: 'Target Android users'
    },
    {
      name: 'beta_testers',
      expression: 'user.userProperty["beta_tester"] == "true"',
      description: 'Target beta testers'
    }
  ];

  conditions.forEach(condition => {
    console.log(`Condition: ${condition.name}`);
    console.log(`  Expression: ${condition.expression}`);
    console.log(`  Description: ${condition.description}`);
    console.log('');
  });
}

// Run the setup
setupRemoteConfig();
generateSampleConditions();
