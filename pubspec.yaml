name: resume_builder
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  cached_network_image: ^3.4.1
  cloud_firestore: ^6.0.0
  cupertino_icons: ^1.0.8
  equatable: ^2.0.5
  file_picker: ^8.1.2
  firebase_ai: ^3.0.0
  firebase_analytics: ^12.0.0
  firebase_auth: ^6.0.0
  firebase_core: ^4.0.0
  firebase_remote_config: ^6.0.0
  firebase_storage: ^13.0.0
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_dotenv: ^5.2.1
  flutter_form_builder: ^10.1.0
  flutter_staggered_animations: ^1.1.1
  flutter_svg: ^2.0.10+1
  form_builder_validators: ^11.0.0
  get_it: ^8.0.0
  google_fonts: ^6.2.1
  google_mobile_ads: ^5.2.0
  google_sign_in: ^6.2.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.2.2
  in_app_purchase: ^3.2.3
  intl: ^0.20.2
  json_annotation: ^4.9.0
  lottie: ^3.1.2
  path: ^1.9.0
  path_provider: ^2.1.4
  pdf: ^3.11.1
  printing: ^5.13.2
  provider: ^6.1.2
  shared_preferences: ^2.3.2
  shimmer: ^3.0.0
  sign_in_with_apple: ^6.1.2
  uuid: ^4.5.1

dev_dependencies:

  # Code generation
  build_runner: ^2.4.13

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  hive_generator: ^2.0.1
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
  - .env
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
