# 🔥 Firebase Remote Config Setup Guide

## 📋 Quick Setup Checklist

- [ ] Access Firebase Console
- [ ] Navigate to Remote Config
- [ ] Add all required parameters
- [ ] Set default values
- [ ] Publish configuration
- [ ] Test in your app

## 🚀 Step-by-Step Setup

### Step 1: Access Firebase Console

1. Open [Firebase Console](https://console.firebase.google.com/project/resume-d24cb/config)
2. Select your project: `resume-d24cb`
3. In the left sidebar, click **Remote Config**

### Step 2: Add Parameters

Click **"Add parameter"** for each of the following:

#### 🎨 Template Control Parameters

| Parameter Key | Type | Default Value | Description |
|---------------|------|---------------|-------------|
| `visible_template_ids` | String | `["classic_professional", "modern_gradient", "minimal_black"]` | JSON array of visible template IDs |
| `enable_premium_templates` | Boolean | `true` | Enable premium template features |

#### 📱 Ad Control Parameters

| Parameter Key | Type | Default Value | Description |
|---------------|------|---------------|-------------|
| `show_banner_ads` | Boolean | `true` | Show banner advertisements |
| `show_interstitial_ads` | Boolean | `true` | Show interstitial advertisements |
| `ad_frequency` | Number | `3` | Show ads every N actions |

#### ⚙️ App Settings Parameters

| Parameter Key | Type | Default Value | Description |
|---------------|------|---------------|-------------|
| `max_free_resumes` | Number | `3` | Maximum free resumes per user |
| `enable_dark_mode` | Boolean | `true` | Enable dark mode option |
| `enable_google_sign_in` | Boolean | `true` | Enable Google Sign-In |
| `enable_apple_sign_in` | Boolean | `true` | Enable Apple Sign-In |

#### 📞 Contact & Legal Parameters

| Parameter Key | Type | Default Value | Description |
|---------------|------|---------------|-------------|
| `support_email` | String | `<EMAIL>` | Support contact email |
| `privacy_policy_url` | String | `""` | Privacy policy URL |
| `terms_of_service_url` | String | `""` | Terms of service URL |

#### 📊 Analytics Parameters

| Parameter Key | Type | Default Value | Description |
|---------------|------|---------------|-------------|
| `enable_analytics` | Boolean | `true` | Enable analytics tracking |
| `enable_crashlytics` | Boolean | `true` | Enable crash reporting |
| `enable_push_notifications` | Boolean | `true` | Enable push notifications |

#### 🔧 App Control Parameters

| Parameter Key | Type | Default Value | Description |
|---------------|------|---------------|-------------|
| `app_version` | String | `1.0.0` | Current app version |
| `force_update` | Boolean | `false` | Force users to update |
| `update_message` | String | `A new version is available. Please update to continue.` | Force update message |
| `maintenance_mode` | Boolean | `false` | Put app in maintenance mode |
| `maintenance_message` | String | `We are currently performing maintenance. Please try again later.` | Maintenance message |

### Step 3: Publish Configuration

1. After adding all parameters, click **"Publish changes"**
2. Add a description for your changes (e.g., "Initial Remote Config setup")
3. Click **"Publish"**

## 🎯 Template Visibility Control

### Available Template IDs:
- `classic_professional` (Free)
- `executive_blue` (Premium)
- `modern_gradient` (Free)
- `tech_stack` (Premium)
- `creative_portfolio` (Premium)
- `minimal_black` (Free)
- `minimal_green` (Premium)

### Configuration Examples:

**Show All Templates:**
```json
[]
```

**Show Only Free Templates:**
```json
["classic_professional", "modern_gradient", "minimal_black"]
```

**Show Only Premium Templates:**
```json
["executive_blue", "tech_stack", "creative_portfolio", "minimal_green"]
```

**Hide Specific Template:**
Remove the template ID from the array.

## 🧪 Testing Your Configuration

### Method 1: Use the Demo Page
1. Run your app: `flutter run`
2. Navigate to `/remote-config-demo` route
3. Check the Remote Config status
4. Verify template filtering works

### Method 2: Use Template Page
1. Go to Templates page in your app
2. Click the refresh button (🔄) in the app bar
3. Observe which templates are visible
4. Change `visible_template_ids` in Firebase Console
5. Refresh again to see changes

### Method 3: Check Logs
Look for Remote Config logs in your Flutter console:
```
Remote Config initialization failed: [error]
Remote Config loaded successfully
```

## 🎛️ Advanced Configuration

### User Targeting with Conditions

Create conditions to target specific user groups:

1. In Remote Config, click **"Add condition"**
2. Name your condition (e.g., `premium_users`)
3. Add targeting rules:

**Target Premium Users:**
```
user.userProperty["subscription"] == "premium"
```

**Target iOS Users:**
```
device.os == "ios"
```

**Target New Users:**
```
user.firstOpenTime > timestamp("2024-01-01T00:00:00Z")
```

### A/B Testing Setup

1. Create two conditions: `group_a` and `group_b`
2. Use percentage targeting:
   - Group A: 50% of users
   - Group B: 50% of users
3. Set different values for each group
4. Monitor app analytics to compare performance

## 🔍 Troubleshooting

### Configuration Not Loading
- Check Firebase project ID matches your app
- Verify internet connection
- Check Firebase console for errors
- Ensure Remote Config is enabled for your project

### Templates Not Filtering
- Verify `visible_template_ids` is a valid JSON array
- Check template IDs match exactly (case-sensitive)
- Refresh templates using the refresh button
- Check app logs for Remote Config errors

### Default Values Not Working
- Ensure default values are set in Firebase Console
- Check data types match (String, Boolean, Number)
- Verify parameter keys are spelled correctly

## 📱 Integration Examples

### Check if Feature is Enabled
```dart
final remoteConfigCubit = context.read<RemoteConfigCubit>();
bool isPremiumEnabled = remoteConfigCubit.isFeatureEnabled('enable_premium_templates');
```

### Filter Templates
```dart
TemplateFilter(
  templates: allTemplates,
  getTemplateId: (template) => template.id,
  builder: (visibleTemplates) {
    return TemplateGrid(templates: visibleTemplates);
  },
)
```

### Control Ad Display
```dart
AdWidget(
  adType: AdType.banner,
  child: BannerAdWidget(),
  fallback: SizedBox.shrink(),
)
```

### Feature Flags
```dart
FeatureFlag(
  featureKey: 'enable_premium_templates',
  child: PremiumFeaturesWidget(),
  fallback: ComingSoonWidget(),
)
```

## 🎉 You're All Set!

Your Firebase Remote Config is now ready to control your app remotely. You can:

- ✅ Toggle template visibility
- ✅ Control ad display
- ✅ Enable/disable features
- ✅ Manage app settings
- ✅ Handle maintenance mode
- ✅ Force app updates

Start experimenting with different configurations and see how they affect your app in real-time!
