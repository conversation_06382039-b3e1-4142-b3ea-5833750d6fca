# Keystore Setup Guide for Resume Builder

This guide will help you set up app signing for your Resume Builder app, which is required for in-app purchases to work in production.

## Step 1: Generate Keystore

### Option A: Using Terminal (Recommended)
```bash
# Navigate to your project
cd /Volumes/SSD/loom_dynamics/my\ sample/resume_builder

# Generate keystore
keytool -genkey -v -keystore android/app/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

### Option B: Using Android Studio
1. Open Android Studio
2. Go to **Build** → **Generate Signed Bundle/APK**
3. Choose **Android App Bundle**
4. Click **Create new...**
5. Fill in the keystore information

## Step 2: Fill in Keystore Information

When prompted, enter:
```
Enter keystore password: [Choose a strong password]
Re-enter new password: [Same password]
What is your first and last name?
  [Unknown]:  Your Name
What is the name of your organizational unit?
  [Unknown]:  App Development
What is the name of your organization?
  [Unknown]:  Your Company
What is the name of your City or Locality?
  [Unknown]:  Your City
What is the name of your State or Province?
  [Unknown]:  Your State
What is the two-letter country code for this unit?
  [Unknown]:  US
Is CN=Your Name, OU=App Development, O=Your Company, L=Your City, ST=Your State, C=US correct?
  [no]:  yes

Enter key password for <upload>
        (RETURN if same as keystore password): [Press Enter or use same password]
```

## Step 3: Update key.properties

Edit `android/key.properties` with your actual passwords:

```properties
storePassword=your_actual_keystore_password
keyPassword=your_actual_key_password
keyAlias=upload
storeFile=upload-keystore.jks
```

**⚠️ IMPORTANT**: 
- Replace `your_actual_keystore_password` with your real password
- Replace `your_actual_key_password` with your real key password
- **Never commit this file to version control**

## Step 4: Add to .gitignore

Make sure these files are in your `.gitignore`:

```gitignore
# Keystore files
android/app/upload-keystore.jks
android/key.properties

# Other sensitive files
android/app/google-services.json
```

## Step 5: Build Signed APK/AAB

### For Google Play Store (AAB - Recommended):
```bash
flutter build appbundle --release
```

### For Direct Distribution (APK):
```bash
flutter build apk --release
```

## Step 6: Verify Signing

Check if your app is properly signed:
```bash
# For AAB
jarsigner -verify -verbose -certs build/app/outputs/bundle/release/app-release.aab

# For APK
jarsigner -verify -verbose -certs build/app/outputs/flutter-apk/app-release.apk
```

## File Structure After Setup

```
android/
├── app/
│   ├── upload-keystore.jks          # Your keystore file
│   ├── proguard-rules.pro           # ProGuard rules
│   └── build.gradle.kts             # Updated with signing config
└── key.properties                   # Keystore credentials (DO NOT COMMIT)
```

## Troubleshooting

### Common Issues:

1. **"keystore not found"**
   - Check file path in `key.properties`
   - Ensure keystore file exists in `android/app/`

2. **"wrong password"**
   - Verify passwords in `key.properties`
   - Make sure no extra spaces or characters

3. **"alias not found"**
   - Check keyAlias in `key.properties`
   - Default should be "upload"

### Debug Commands:

```bash
# List keystore contents
keytool -list -v -keystore android/app/upload-keystore.jks

# Check keystore alias
keytool -list -keystore android/app/upload-keystore.jks
```

## Security Best Practices

1. **Backup your keystore**:
   - Store in secure location (cloud storage, password manager)
   - If lost, you cannot update your app on Google Play

2. **Keep passwords secure**:
   - Use strong, unique passwords
   - Store in password manager
   - Never share or commit to version control

3. **Use App Signing by Google Play** (Recommended):
   - Upload signing key to Google Play
   - Google manages the signing process
   - More secure and allows key recovery

## Google Play Console Integration

After building with your keystore:

1. **Upload to Play Console**:
   - Go to **Release** → **Testing** → **Internal testing**
   - Upload your signed AAB/APK

2. **Verify App Signing**:
   - Go to **Setup** → **App signing**
   - Check that your certificates are properly configured

3. **Enable App Signing by Google Play**:
   - Recommended for production apps
   - Allows Google to manage signing keys

## Next Steps

1. ✅ Generate keystore
2. ✅ Update `key.properties` with real passwords
3. ✅ Build signed AAB: `flutter build appbundle --release`
4. ✅ Upload to Google Play Console
5. ✅ Create in-app products (requires signed APK uploaded)
6. ✅ Test in-app purchases

## Important Notes

- **Keystore is required** for in-app purchases to work in production
- **Debug signing** won't work with Google Play Billing
- **Keep your keystore safe** - losing it means you can't update your app
- **Test thoroughly** with signed builds before publishing

Your app is now ready for production deployment with in-app purchases! 🚀
