# 🚨 PRICING PLANS NOT SHOWING - QUICK FIX GUIDE

## 🔍 **IMMEDIATE DIAGNOSIS**

Run your app and check the debug console for these messages:

```
DEBUG: PricingPlansWidget - Current state: [StateType]
DEBUG: PricingPlansWidget - [Additional info]
```

## 📊 **POSSIBLE STATES & FIXES**

### 1. **State: PurchaseInitial** 
**What you see:** "Loading pricing plans..." with "Initialize Purchases" button
**Problem:** PurchaseCubit was never initialized
**Fix:** 
```dart
// Make sure this is called when the widget loads
context.read<PurchaseCubit>().initialize();
```

### 2. **State: PurchaseLoading**
**What you see:** Loading spinner
**Problem:** Stuck in loading state
**Fix:** Check for initialization errors in console

### 3. **State: PurchaseError**
**What you see:** Error message with "Retry" button
**Problem:** Purchase system failed to initialize
**Common causes:**
- In-app purchases not available on device/emulator
- Network issues
- Google Play Services not available

### 4. **State: PurchaseLoaded with 0 products**
**What you see:** "No pricing plans available" with red icon
**Problem:** Products not found in Google Play Console
**This is the most common issue!**

## 🎯 **MOST LIKELY ISSUE: Products Not Found**

If you see "No pricing plans available", the problem is:

### ✅ **Quick Checklist:**
1. **Products exist in Google Play Console?**
   - Go to Play Console → Your App → Monetize → Products → In-app products
   - Verify `premium_monthly`, `premium_yearly`, `premium_lifetime` exist
   - Status must be **Active** (not Inactive)

2. **App is signed with release key?**
   - Debug builds won't see Play Console products
   - Must use: `flutter build appbundle --release`

3. **Testing from Internal Testing track?**
   - Can't test with sideloaded APK
   - Must install from Google Play Internal Testing

4. **Test account added to License Testing?**
   - Play Console → Setup → License testing
   - Add your Gmail account

## 🛠️ **STEP-BY-STEP FIX**

### Step 1: Use the Debug Page
Add this to your app temporarily:

```dart
// Add to your navigation
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const PricingDebugPage(),
  ),
);
```

### Step 2: Check Google Play Console
1. Open [Google Play Console](https://play.google.com/console)
2. Select your app
3. Go to **Monetize** → **Products** → **In-app products**
4. Verify these products exist and are **Active**:
   - `premium_monthly`
   - `premium_yearly` 
   - `premium_lifetime`

### Step 3: Create Missing Products
If products don't exist, create them:

```
Product ID: premium_monthly
Name: Premium Monthly
Description: Get unlimited PDF exports and premium features for one month
Status: Active
Price: $4.99
```

### Step 4: Build and Test Correctly
```bash
# Build release version
flutter build appbundle --release

# Upload to Play Console Internal Testing
# Install from Play Store (not sideload)
```

### Step 5: Check Debug Output
Look for these specific messages:

```
✅ SUCCESS:
DEBUG: IAP - Found products: premium_monthly, premium_yearly, premium_lifetime
DEBUG: PricingPlansWidget - Loaded with 3 products

❌ FAILURE:
DEBUG: IAP - Products not found: [premium_monthly, premium_yearly, premium_lifetime]
DEBUG: PricingPlansWidget - NO PRODUCTS FOUND!
```

## 🚀 **QUICK TEST**

Add this temporary button to test immediately:

```dart
ElevatedButton(
  onPressed: () async {
    final cubit = context.read<PurchaseCubit>();
    await cubit.initialize();
    
    final state = cubit.state;
    if (state is PurchaseLoaded) {
      print('✅ Products loaded: ${state.products.length}');
      for (final product in state.products) {
        print('  - ${product.id}: ${product.title}');
      }
    } else {
      print('❌ State: ${state.runtimeType}');
    }
  },
  child: Text('Test Pricing Plans'),
)
```

## 🔧 **COMMON FIXES**

### Fix 1: BlocProvider Missing
Make sure PurchaseCubit is provided:

```dart
BlocProvider(
  create: (context) => getIt<PurchaseCubit>()..initialize(),
  child: const PricingPlansWidget(),
)
```

### Fix 2: Initialize Not Called
```dart
@override
void initState() {
  super.initState();
  context.read<PurchaseCubit>().initialize();
}
```

### Fix 3: Wrong Product IDs
Check that your code matches Play Console exactly:
- Case sensitive: `premium_monthly` not `Premium_Monthly`
- No spaces or special characters
- Exactly as created in Play Console

## 🆘 **STILL NOT WORKING?**

1. **Wait 2-4 hours** after creating products in Play Console
2. **Clear Google Play Store cache**
3. **Try different test account**
4. **Check Google Play Console status page**
5. **Use the debug tools I created**

## 📱 **Testing Checklist**

- [ ] Real device (not emulator)
- [ ] Release build (not debug)
- [ ] Installed from Internal Testing
- [ ] Test account in License Testing
- [ ] Products exist and are Active
- [ ] Google Play Store updated
- [ ] Network connection working

## 🎯 **Expected Result**

When working correctly, you should see:
- Beautiful pricing cards for each product
- Correct prices and titles
- "MOST POPULAR" badge on yearly plan
- "BEST VALUE" badge on lifetime plan
- Working purchase buttons

The debug output should show:
```
DEBUG: PricingPlansWidget - Current state: PurchaseLoaded
DEBUG: PricingPlansWidget - Loaded with 3 products
```

**Remember: 90% of the time, the issue is products not created or not active in Google Play Console!**
