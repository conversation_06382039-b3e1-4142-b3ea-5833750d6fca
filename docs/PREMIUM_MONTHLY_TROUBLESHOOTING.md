# Premium Monthly Not Showing - Troubleshooting Guide

## Quick Diagnosis Steps

### Step 1: Check Debug Output
Run your app and look for these debug messages in the console:

```
DEBUG: IAP - Loading products: [premium_monthly, premium_yearly, premium_lifetime]
DEBUG: IAP - Found products: [list of found products]
DEBUG: IAP - Products not found: [list of missing products]
```

If you see `premium_monthly` in the "Products not found" list, the issue is in Google Play Console setup.

### Step 2: Use the Debug Tool
I've created a debug page for you. Add this to your app temporarily:

```dart
// Add to your main app navigation or create a debug route
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const PurchaseDebugPage(),
  ),
);
```

## Common Issues and Solutions

### Issue 1: Product Not Created in Google Play Console
**Symptoms:** `premium_monthly` appears in "Products not found" debug log

**Solution:**
1. Go to [Google Play Console](https://play.google.com/console)
2. Select your app
3. Navigate to **Monetize** → **Products** → **In-app products**
4. Click **"Create product"**
5. Fill in exactly:
   ```
   Product ID: premium_monthly
   Name: Premium Monthly
   Description: Get unlimited PDF exports and premium features for one month
   Status: Active
   Price: $4.99 (or your preferred price)
   ```
6. **Important:** Make sure Status is set to **Active**

### Issue 2: App Not Signed with Release Key
**Symptoms:** Products load in debug but not in release builds

**Solution:**
1. Build with release key: `flutter build appbundle --release`
2. Upload to Google Play Console Internal Testing
3. Install from Internal Testing track (not sideloaded APK)

### Issue 3: Product Status is Inactive
**Symptoms:** Product exists but doesn't load

**Solution:**
1. In Google Play Console, go to your product
2. Check that Status is **Active**
3. If it's **Inactive**, activate it
4. Wait 2-4 hours for changes to propagate

### Issue 4: App Version Mismatch
**Symptoms:** Products work in one version but not another

**Solution:**
1. Ensure you're testing with the same app version uploaded to Play Console
2. Version code in `pubspec.yaml` should match uploaded version
3. Upload new version if needed

### Issue 5: Testing Account Issues
**Symptoms:** Products don't load for test accounts

**Solution:**
1. Go to **Setup** → **License testing** in Google Play Console
2. Add your test Gmail account
3. Set license response to **RESPOND_NORMALLY**
4. Clear Google Play Store cache: Settings → Apps → Google Play Store → Storage → Clear Cache

## Verification Checklist

### Google Play Console Setup
- [ ] Product `premium_monthly` exists
- [ ] Product ID is exactly `premium_monthly` (case-sensitive)
- [ ] Product status is **Active**
- [ ] App is uploaded to at least Internal Testing
- [ ] Test account is added to License Testing

### App Configuration
- [ ] Product ID in code matches exactly: `premium_monthly`
- [ ] App is signed with release key
- [ ] App version matches uploaded version
- [ ] In-app purchase permission is in AndroidManifest.xml

### Testing Environment
- [ ] Testing on real device (not emulator)
- [ ] App installed from Google Play (Internal Testing)
- [ ] Signed in with test account
- [ ] Google Play Store is up to date

## Debug Commands

### Check App Signature
```bash
# Check if your APK is signed correctly
keytool -printcert -jarfile app-release.apk
```

### Clear Google Play Store Data
```bash
# Clear Play Store cache and data
adb shell pm clear com.android.vending
```

### Check Logcat for IAP Messages
```bash
# Filter for in-app purchase related logs
adb logcat | grep -E "(IAP|InAppPurchase|BillingClient)"
```

## Advanced Debugging

### Enable Verbose Logging
Add this to your `main.dart` for more detailed logs:

```dart
void main() {
  // Enable debug logging
  if (kDebugMode) {
    debugPrint('DEBUG: App starting with IAP debug enabled');
  }
  runApp(MyApp());
}
```

### Test Product Query Directly
Use the `PurchaseDebugPage` I created to test product loading directly.

## Expected Debug Output (Success)
When everything works correctly, you should see:

```
DEBUG: IAP - Loading products: [premium_monthly, premium_yearly, premium_lifetime]
DEBUG: IAP - Found products: premium_monthly, premium_yearly, premium_lifetime
DEBUG: IAP - Successfully loaded 3 products
DEBUG: IAP - ✅ Available: premium_monthly - Premium Monthly ($4.99)
DEBUG: IAP - ✅ Available: premium_yearly - Premium Yearly ($29.99)
DEBUG: IAP - ✅ Available: premium_lifetime - Premium Lifetime ($99.99)
DEBUG: Premium Page - Loaded 3 products
```

## Still Not Working?

If `premium_monthly` still doesn't show after following this guide:

1. **Wait 2-4 hours** after making changes in Google Play Console
2. **Try a different test account** that's added to License Testing
3. **Check Google Play Console status page** for any service issues
4. **Contact Google Play Support** if the issue persists

## Quick Test Script

Run this in your app to quickly test:

```dart
void testPremiumMonthly() async {
  final purchaseUseCases = getIt<PurchaseUseCases>();
  
  print('🔄 Testing premium_monthly...');
  
  final initialized = await purchaseUseCases.initialize();
  print('Initialized: $initialized');
  
  final products = await purchaseUseCases.getAvailableProducts();
  print('Total products: ${products.length}');
  
  final monthlyProduct = products.where((p) => p.id == 'premium_monthly').firstOrNull;
  if (monthlyProduct != null) {
    print('✅ premium_monthly found: ${monthlyProduct.title} (${monthlyProduct.price})');
  } else {
    print('❌ premium_monthly NOT FOUND');
    print('Available products: ${products.map((p) => p.id).join(', ')}');
  }
}
```

Remember: The most common issue is that the product isn't created or activated in Google Play Console!
