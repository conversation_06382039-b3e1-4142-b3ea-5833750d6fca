# Remote Config Purchase Control

This document explains how to use Firebase Remote Config to remotely enable or disable in-app purchases in the Resume Builder app.

## 🎯 Overview

The app now supports remotely controlling in-app purchases through Firebase Remote Config. This allows you to:

- **Disable purchases globally** during maintenance or policy updates
- **Enable/disable purchases by region** (future enhancement)
- **Control purchase availability** without app updates
- **Provide user-friendly messaging** when purchases are unavailable

## 🔧 Configuration

### Firebase Remote Config Parameter

**Parameter Name:** `enable_in_app_purchases`
**Type:** Boolean
**Default Value:** `true`

### Setting Up in Firebase Console

1. Go to Firebase Console → Remote Config
2. Add a new parameter:
   - **Parameter key:** `enable_in_app_purchases`
   - **Default value:** `true`
   - **Description:** Controls whether in-app purchases are available to users

3. To disable purchases:
   - Set value to `false`
   - Publish changes
   - Users will see the disabled state within the fetch interval

## 🎨 User Experience

### When Purchases Are Enabled (Default)
- Normal purchase flow works as expected
- Users can see pricing plans and make purchases
- All purchase-related UI is fully functional

### When Purchases Are Disabled
- **Pricing Plans Widget:** Shows "Purchases Temporarily Unavailable" message
- **Export Limit Dialog:** Displays disabled state instead of purchase options
- **Premium Upgrade Page:** Shows maintenance message with appropriate icon
- **Purchase Buttons:** Disabled with informative messaging

## 🔄 How It Works

### Architecture

```
User Action → PurchaseCubit → RemoteConfigRepository → Firebase Remote Config
                ↓
         PurchaseAvailabilityService → InAppPurchaseService
                ↓
            UI Components (Disabled State)
```

### Key Components

1. **PurchaseAvailabilityService**
   - Centralized service for checking purchase availability
   - Provides user-friendly error messages
   - Handles fallback behavior

2. **Remote Config Integration**
   - New `enable_in_app_purchases` parameter
   - Automatic checking in purchase initialization
   - Real-time updates when config changes

3. **UI State Management**
   - New `PurchaseDisabled` state in PurchaseCubit
   - Graceful degradation in all purchase-related widgets
   - Consistent messaging across the app

## 🚀 Usage Examples

### Checking Purchase Availability

```dart
// In a service or use case
final availabilityService = PurchaseAvailabilityService(remoteConfigRepository);

if (availabilityService.arePurchasesEnabled()) {
  // Proceed with purchase flow
} else {
  // Show disabled message
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('Purchases Unavailable'),
      content: Text(availabilityService.getDisabledMessage()),
    ),
  );
}
```

### Remote Config Values

```json
{
  "enable_in_app_purchases": true,  // Enable purchases
  "enable_in_app_purchases": false  // Disable purchases
}
```

## 🛠️ Testing

### Manual Testing

1. **Enable Purchases:**
   - Set `enable_in_app_purchases` to `true` in Firebase Console
   - Restart app or wait for config fetch
   - Verify normal purchase flow works

2. **Disable Purchases:**
   - Set `enable_in_app_purchases` to `false` in Firebase Console
   - Force refresh remote config or restart app
   - Verify disabled state appears in all purchase UIs

### Automated Testing

Run the test suite:
```bash
flutter test test/core/services/purchase_availability_service_test.dart
```

## 🔍 Monitoring

### What to Monitor

- **Remote Config Fetch Success Rate:** Ensure config updates reach users
- **Purchase Attempt Errors:** Monitor for blocked purchase attempts
- **User Feedback:** Watch for confusion when purchases are disabled

### Logging

The app logs purchase availability checks:
```
DEBUG: IAP - In-App Purchases disabled via remote config
DEBUG: Purchase Cubit - Purchases disabled: [message]
```

## 🚨 Emergency Procedures

### Quickly Disable Purchases

1. Go to Firebase Console → Remote Config
2. Set `enable_in_app_purchases` to `false`
3. Click "Publish changes"
4. Users will see disabled state within fetch interval (typically 1-12 hours)

### Re-enable Purchases

1. Set `enable_in_app_purchases` to `true`
2. Publish changes
3. Monitor for successful re-enablement

## 📱 Platform Considerations

### iOS
- Respects remote config settings
- Shows appropriate App Store compliance messaging

### Android
- Integrates with Google Play Billing
- Handles Play Store policy requirements

## 🔮 Future Enhancements

- **Regional Control:** Disable purchases in specific countries
- **User Segment Control:** Disable for specific user groups
- **Scheduled Control:** Automatic enable/disable based on time
- **A/B Testing:** Test different purchase flows
- **Gradual Rollout:** Enable purchases for percentage of users

## 📞 Support

If you encounter issues with remote config purchase control:

1. Check Firebase Console for config status
2. Verify app has latest remote config values
3. Check device logs for purchase availability messages
4. Contact development team with specific error details
